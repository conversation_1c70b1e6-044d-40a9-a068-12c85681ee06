#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修正：专门修正塘贝村数据不匹配问题
基于真实的明细表数据更新汇总表58-66行
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_塘贝村修正_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def extract_tangbei_data():
    """从明细表提取塘贝村的真实数据"""
    df = pd.read_excel('a1.xlsx', sheet_name='1.江高镇', header=None)
    
    tangbei_data = {}
    current_community = None
    
    for i in range(len(df)):
        row = df.iloc[i]
        
        # 找到塘贝村
        if pd.notna(row[1]) and '塘贝' in str(row[1]):
            current_community = '塘贝村'
            print(f"找到塘贝村: 第{i+1}行")
        
        # 如果当前是塘贝村，收集环境类型数据
        if current_community == '塘贝村' and pd.notna(row[2]):
            env_type = str(row[2]).strip()
            if env_type and env_type != 'nan':
                # 只使用第1组数据（列6-9），因为其他组都是NaN
                households = pd.to_numeric(row[5], errors='coerce') if len(row) > 5 else 0  # 列6
                positive = pd.to_numeric(row[6], errors='coerce') if len(row) > 6 else 0     # 列7
                bi_index = pd.to_numeric(row[7], errors='coerce') if len(row) > 7 else 0     # 列8
                adi_density = pd.to_numeric(row[8], errors='coerce') if len(row) > 8 else 0  # 列9
                
                # 处理NaN值
                households = households if not pd.isna(households) else 0
                positive = positive if not pd.isna(positive) else 0
                bi_index = bi_index if not pd.isna(bi_index) else 0
                adi_density = adi_density if not pd.isna(adi_density) else 0
                
                tangbei_data[env_type] = {
                    '户数': households,
                    '阳性': positive,
                    'BI': bi_index,
                    'ADI': adi_density
                }
                
                print(f"  {env_type}: 户数={households}, 阳性={positive}, BI={bi_index}, ADI={adi_density}")
    
    return tangbei_data

def update_tangbei_summary(file_path, tangbei_data):
    """更新汇总表第58-66行的塘贝村数据"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        # 环境类型顺序
        env_order = ['居民区', '公园景区', '医疗机构', '学校', '福利机构', '建筑工地', '闲置房屋', '商贸区', '其他']
        
        print("\n更新汇总表第58-66行:")
        
        for i, env_type in enumerate(env_order):
            row_idx = 58 + i
            
            if env_type in tangbei_data:
                data = tangbei_data[env_type]
                
                # 更新数据
                ws.cell(row=row_idx, column=4, value=data['户数'])      # D列：监测户数
                ws.cell(row=row_idx, column=5, value=data['阳性'])      # E列：阳性数
                ws.cell(row=row_idx, column=7, value=data['BI'])        # G列：BI指数
                ws.cell(row=row_idx, column=8, value=data['ADI'])       # H列：ADI密度
                
                # 计算F列：阳性率 = E/D * 100
                if data['户数'] > 0:
                    f_val = round((data['阳性'] / data['户数']) * 100, 2)
                else:
                    f_val = 0.0
                ws.cell(row=row_idx, column=6, value=f_val)             # F列：阳性率
                
                # 格式化数字列
                for col in range(4, 9):  # D到H列
                    cell = ws.cell(row=row_idx, column=col)
                    if col != 9:  # 除了I列
                        cell.number_format = '0.00'
                
                print(f"  第{row_idx}行 {env_type}: 户数={data['户数']}, 阳性={data['阳性']}, "
                      f"阳性率={f_val}%, BI={data['BI']}, ADI={data['ADI']}")
            else:
                # 没有数据的环境类型，设置为0
                ws.cell(row=row_idx, column=4, value=0)
                ws.cell(row=row_idx, column=5, value=0)
                ws.cell(row=row_idx, column=6, value=0)
                ws.cell(row=row_idx, column=7, value=0)
                ws.cell(row=row_idx, column=8, value=0)
                
                for col in range(4, 9):
                    cell = ws.cell(row=row_idx, column=col)
                    if col != 9:
                        cell.number_format = '0.00'
                
                print(f"  第{row_idx}行 {env_type}: 无数据，设置为0")
        
        # 保存文件
        wb.save(file_path)
        return True
        
    except Exception as e:
        print(f"更新失败: {e}")
        return False

def verify_tangbei_fix(file_path):
    """验证塘贝村数据修正结果"""
    wb = load_workbook(file_path)
    ws = wb['汇总']
    
    print("\n验证修正结果:")
    print("汇总表第58-66行（塘贝村）:")
    
    env_order = ['居民区', '公园景区', '医疗机构', '学校', '福利机构', '建筑工地', '闲置房屋', '商贸区', '其他']
    
    for i, env_type in enumerate(env_order):
        row_idx = 58 + i
        
        town = ws.cell(row=row_idx, column=1).value
        community = ws.cell(row=row_idx, column=2).value
        env = ws.cell(row=row_idx, column=3).value
        d_val = ws.cell(row=row_idx, column=4).value
        e_val = ws.cell(row=row_idx, column=5).value
        f_val = ws.cell(row=row_idx, column=6).value
        g_val = ws.cell(row=row_idx, column=7).value
        h_val = ws.cell(row=row_idx, column=8).value
        
        community_display = community if community and str(community) != 'None' else '[合并单元格]'
        print(f"  第{row_idx}行: {town}-{community_display}-{env}")
        print(f"    户数:{d_val}, 阳性:{e_val}, 阳性率:{f_val}%, BI:{g_val}, ADI:{h_val}")

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🎯 专门修正塘贝村数据不匹配问题")
    print("=" * 60)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 2. 从明细表提取塘贝村真实数据
        print("\n📊 从明细表提取塘贝村真实数据:")
        tangbei_data = extract_tangbei_data()
        
        if not tangbei_data:
            print("❌ 未能提取到塘贝村数据")
            return
        
        print(f"\n✅ 成功提取塘贝村 {len(tangbei_data)} 种环境类型的数据")
        
        # 3. 更新汇总表58-66行
        success = update_tangbei_summary(file_path, tangbei_data)
        
        if success:
            print("\n🎉 塘贝村数据修正完成！")
            
            # 4. 验证修正结果
            verify_tangbei_fix(file_path)
            
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            print(f"\n✅ 修正说明:")
            print(f"   • 汇总表第58-66行已更新为塘贝村的真实数据")
            print(f"   • 数据来源：明细表1.江高镇第60-68行")
            print(f"   • F列阳性率公式：E/D×100")
            print(f"   • 所有数值格式化为保留2位小数")
            
        else:
            print("\n❌ 塘贝村数据修正失败")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    import os
    main()
