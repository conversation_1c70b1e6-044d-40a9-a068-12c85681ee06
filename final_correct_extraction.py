#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修正版：正确处理4个日期组的数据
明细表结构：
- 第1组: 列6-9 (监测户数、阳性数、BI、ADI)
- 第2组: 列11-14 (监测户数、阳性数、BI、ADI)  
- 第3组: 列16-19 (监测户数、阳性数、BI、ADI)
- 第4组: 列21-24 (监测户数、阳性数、BI、ADI)
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
from datetime import datetime
from collections import defaultdict

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_最终修正_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def normalize_name(name):
    """标准化名称"""
    if pd.isna(name) or name is None:
        return ""
    
    name_str = str(name).strip()
    name_str = name_str.replace('\n', '').replace('\r', '').strip()
    name_str = name_str.replace('居委会', '').replace('居委', '').replace('村委会', '').replace('村委', '')
    name_str = name_str.replace('村', '').replace('社区', '').strip()
    
    return name_str

def extract_all_date_groups_data(file_path):
    """提取所有4个日期组的数据并汇总"""
    excel_file = pd.ExcelFile(file_path)
    detail_sheets = [name for name in excel_file.sheet_names 
                    if name != '汇总' and 'WpsReserved' not in name and name.strip() != '']
    
    summary_data = defaultdict(lambda: {
        '监测户数': 0,
        '阳性数': 0,
        '布雷图指数_values': [],
        '雌蚊密度_values': []
    })
    
    processing_log = []
    
    # 4个日期组的列索引 (转换为0-based索引)
    date_groups = [
        (5, 6, 7, 8),    # 第1组: 列6-9
        (10, 11, 12, 13), # 第2组: 列11-14
        (15, 16, 17, 18), # 第3组: 列16-19
        (20, 21, 22, 23)  # 第4组: 列21-24
    ]
    
    for sheet_name in detail_sheets:
        try:
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
            
            current_community = None
            records_count = 0
            
            print(f"处理 {town_name}...")
            
            for i in range(len(df_raw)):
                row = df_raw.iloc[i]
                
                # 识别新的居委
                if pd.notna(row[0]) and str(row[0]).strip().isdigit():
                    community_info = str(row[1]).strip() if pd.notna(row[1]) else f"居委{row[0]}"
                    current_community = normalize_name(community_info)
                    if current_community:
                        print(f"  找到居委: {current_community}")
                
                # 处理环境类型数据
                if pd.notna(row[2]) and current_community:
                    env_type = str(row[2]).strip()
                    
                    if not env_type or env_type.lower() in ['nan', 'none']:
                        continue
                    
                    key = (town_name, current_community, env_type)
                    
                    # 处理4个日期组的数据
                    for group_idx, (h_col, p_col, bi_col, adi_col) in enumerate(date_groups):
                        if len(row) > max(h_col, p_col, bi_col, adi_col):
                            households = pd.to_numeric(row[h_col], errors='coerce') if pd.notna(row[h_col]) else 0
                            positive = pd.to_numeric(row[p_col], errors='coerce') if pd.notna(row[p_col]) else 0
                            bi_index = pd.to_numeric(row[bi_col], errors='coerce') if pd.notna(row[bi_col]) else 0
                            adi_density = pd.to_numeric(row[adi_col], errors='coerce') if pd.notna(row[adi_col]) else 0
                            
                            # 处理NaN值
                            households = households if not pd.isna(households) else 0
                            positive = positive if not pd.isna(positive) else 0
                            bi_index = bi_index if not pd.isna(bi_index) else 0
                            adi_density = adi_density if not pd.isna(adi_density) else 0
                            
                            if households > 0 or positive > 0 or bi_index > 0 or adi_density > 0:
                                summary_data[key]['监测户数'] += households
                                summary_data[key]['阳性数'] += positive
                                
                                if bi_index > 0:
                                    summary_data[key]['布雷图指数_values'].append(bi_index)
                                
                                if adi_density > 0:
                                    summary_data[key]['雌蚊密度_values'].append(adi_density)
                                
                                records_count += 1
                                
                                # 显示塘贝村的详细数据
                                if '塘贝' in current_community and records_count <= 20:
                                    print(f"    {env_type} 第{group_idx+1}组: 户数={households}, 阳性={positive}, BI={bi_index}, ADI={adi_density}")
            
            processing_log.append(f"✓ {town_name}: 处理了 {records_count} 条记录")
            
        except Exception as e:
            processing_log.append(f"✗ {sheet_name}: 处理失败 - {e}")
            continue
    
    # 计算平均值
    for key in summary_data:
        data = summary_data[key]
        
        if data['布雷图指数_values']:
            data['平均布雷图指数'] = round(np.mean(data['布雷图指数_values']), 4)
        else:
            data['平均布雷图指数'] = 0
            
        if data['雌蚊密度_values']:
            data['平均雌蚊密度'] = round(np.mean(data['雌蚊密度_values']), 4)
        else:
            data['平均雌蚊密度'] = 0
    
    return dict(summary_data), processing_log

def update_summary_with_all_data(file_path, summary_data):
    """使用汇总后的数据更新汇总表"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        update_log = []
        updated_count = 0
        
        print("开始更新汇总表...")
        
        for row_idx in range(4, ws.max_row + 1):
            try:
                town_cell = ws.cell(row=row_idx, column=1)
                community_cell = ws.cell(row=row_idx, column=2)
                env_type_cell = ws.cell(row=row_idx, column=3)
                
                town = str(town_cell.value).strip() if town_cell.value else ""
                community = str(community_cell.value).strip() if community_cell.value else ""
                env_type = str(env_type_cell.value).strip() if env_type_cell.value else ""
                
                if not town or not env_type:
                    continue
                
                # 标准化名称进行匹配
                town_normalized = normalize_name(town)
                community_normalized = normalize_name(community)
                
                # 查找匹配的数据
                matched_key = None
                best_match_score = 0
                
                for key in summary_data:
                    key_town, key_community, key_env = key
                    
                    # 镇街匹配
                    town_match = (town_normalized in key_town or key_town in town_normalized or
                                town_normalized.replace('镇', '').replace('街', '') == 
                                key_town.replace('镇', '').replace('街', ''))
                    
                    # 居委匹配
                    community_match = (not community_normalized or 
                                     community_normalized in key_community or 
                                     key_community in community_normalized)
                    
                    # 环境类型精确匹配
                    env_match = (key_env == env_type)
                    
                    if town_match and community_match and env_match:
                        match_score = 1
                        if community_normalized and community_normalized == key_community:
                            match_score += 1
                        
                        if match_score > best_match_score:
                            best_match_score = match_score
                            matched_key = key
                
                if matched_key:
                    data = summary_data[matched_key]
                    
                    # 更新数据
                    ws.cell(row=row_idx, column=4, value=data['监测户数'])
                    ws.cell(row=row_idx, column=5, value=data['阳性数'])
                    ws.cell(row=row_idx, column=7, value=data['平均布雷图指数'])
                    ws.cell(row=row_idx, column=8, value=data['平均雌蚊密度'])
                    
                    # 计算F列：阳性率 = E/D * 100
                    if data['监测户数'] > 0:
                        f_val = round((data['阳性数'] / data['监测户数']) * 100, 2)
                    else:
                        f_val = 0.0
                    ws.cell(row=row_idx, column=6, value=f_val)
                    
                    # 格式化数字列
                    for col in range(4, 9):
                        cell = ws.cell(row=row_idx, column=col)
                        if col != 9:
                            cell.number_format = '0.00'
                    
                    updated_count += 1
                    
                    # 记录塘贝村的更新
                    if '塘贝' in community and updated_count <= 20:
                        update_log.append(f"✓ 第{row_idx}行: {town}-{community}-{env_type} -> "
                                        f"户数:{data['监测户数']}, 阳性:{data['阳性数']}, "
                                        f"阳性率:{f_val}%, BI:{data['平均布雷图指数']}, ADI:{data['平均雌蚊密度']}")
                else:
                    # 没有匹配到数据，设置为0
                    for col in range(4, 9):
                        ws.cell(row=row_idx, column=col, value=0)
                        if col != 9:
                            ws.cell(row=row_idx, column=col).number_format = '0.00'
                    
                    updated_count += 1
                        
            except Exception as e:
                continue
        
        # 保存文件
        wb.save(file_path)
        update_log.append(f"✓ 成功更新 {updated_count} 行数据")
        
        return True, update_log
        
    except Exception as e:
        return False, [f"✗ 更新失败: {e}"]

def verify_tangbei_data(file_path, summary_data):
    """验证塘贝村数据"""
    print("\n🔍 验证塘贝村数据:")
    
    # 显示提取的塘贝村数据
    print("从明细表提取的塘贝村数据:")
    for key, data in summary_data.items():
        town, community, env_type = key
        if '塘贝' in community:
            print(f"  {env_type}: 户数={data['监测户数']}, 阳性={data['阳性数']}, "
                  f"BI={data['平均布雷图指数']}, ADI={data['平均雌蚊密度']}")
    
    # 检查汇总表58-66行
    wb = load_workbook(file_path)
    ws = wb['汇总']
    
    print("\n汇总表58-66行更新后的数据:")
    for row_idx in range(58, 67):
        env_type = ws.cell(row=row_idx, column=3).value
        d_val = ws.cell(row=row_idx, column=4).value
        e_val = ws.cell(row=row_idx, column=5).value
        f_val = ws.cell(row=row_idx, column=6).value
        g_val = ws.cell(row=row_idx, column=7).value
        h_val = ws.cell(row=row_idx, column=8).value
        
        if env_type:
            print(f"  第{row_idx}行 {env_type}: 户数={d_val}, 阳性={e_val}, "
                  f"阳性率={f_val}%, BI={g_val}, ADI={h_val}")

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🎯 最终修正：处理4个日期组数据")
    print("=" * 60)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 2. 提取所有日期组的数据
        print("\n📊 提取并汇总4个日期组的数据...")
        summary_data, processing_log = extract_all_date_groups_data(file_path)
        
        for log in processing_log:
            print(f"   {log}")
        
        if not summary_data:
            print("❌ 未能提取到任何数据")
            return
        
        print(f"\n✅ 成功提取 {len(summary_data)} 条汇总记录")
        
        # 3. 更新汇总表
        print("\n📝 更新汇总表...")
        success, update_log = update_summary_with_all_data(file_path, summary_data)
        
        for log in update_log:
            print(f"   {log}")
        
        if success:
            print("\n🎉 数据修正完成！")
            
            # 4. 验证塘贝村数据
            verify_tangbei_data(file_path, summary_data)
            
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            
        else:
            print("\n❌ 数据修正失败")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    import os
    main()
