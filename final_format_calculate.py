#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版格式化和计算Excel汇总表
完善处理所有列的格式化和计算
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_最终格式化_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def get_density_grade(bi_value, adi_value):
    """根据广州市蚊媒密度分级表确定密度等级"""
    # 处理None和NaN值
    bi = 0
    adi = 0
    
    if bi_value is not None and not pd.isna(bi_value):
        try:
            bi = float(bi_value)
        except (ValueError, TypeError):
            bi = 0
    
    if adi_value is not None and not pd.isna(adi_value):
        try:
            adi = float(adi_value)
        except (ValueError, TypeError):
            adi = 0
    
    # 根据分级表判断等级
    if bi > 20 or adi > 10:
        return "三级"
    elif (10 < bi <= 20) or (5 < adi <= 10):
        return "二级"
    elif (5 < bi <= 10) or (2 < adi <= 5):
        return "一级"
    else:
        return "0级"

def is_data_row(town, env_type, d_value):
    """判断是否是有效的数据行"""
    if not town or not env_type:
        return False
    
    town_str = str(town).strip()
    env_str = str(env_type).strip()
    
    # 跳过表头和汇总行
    if (town_str in ['镇街', ''] or 
        env_str in ['环境类型', ''] or
        '汇总' in town_str or '小计' in town_str or
        d_value is None):
        return False
    
    return True

def safe_float_convert(value, default=0):
    """安全转换为浮点数"""
    if value is None:
        return default
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        if value.startswith('='):  # 跳过公式
            return default
        try:
            return float(value)
        except ValueError:
            return default
    return default

def complete_format_and_calculate(file_path):
    """完整的格式化和计算"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        processed_count = 0
        calculation_log = []
        
        print("开始完整格式化和计算...")
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                # 读取基本信息
                town_cell = ws.cell(row=row_idx, column=1)
                community_cell = ws.cell(row=row_idx, column=2)
                env_cell = ws.cell(row=row_idx, column=3)
                
                town = town_cell.value
                env_type = env_cell.value
                
                # 检查是否是有效数据行
                d_cell = ws.cell(row=row_idx, column=4)
                if not is_data_row(town, env_type, d_cell.value):
                    continue
                
                # 安全获取所有数值
                d_val = safe_float_convert(d_cell.value)
                e_val = safe_float_convert(ws.cell(row=row_idx, column=5).value)
                g_val = safe_float_convert(ws.cell(row=row_idx, column=7).value)
                h_val = safe_float_convert(ws.cell(row=row_idx, column=8).value)
                
                # 计算F列：阳性率 = E列/D列 * 10
                if d_val > 0:
                    f_val = round((e_val / d_val) * 10, 2)
                else:
                    f_val = 0.0
                
                # 计算密度分级
                grade = get_density_grade(g_val, h_val)
                
                # 更新和格式化所有列
                try:
                    # D列：监测户数
                    d_cell.value = d_val
                    d_cell.number_format = '0.00'
                    
                    # E列：阳性数
                    e_cell = ws.cell(row=row_idx, column=5)
                    e_cell.value = e_val
                    e_cell.number_format = '0.00'
                    
                    # F列：阳性率
                    f_cell = ws.cell(row=row_idx, column=6)
                    f_cell.value = f_val
                    f_cell.number_format = '0.00'
                    
                    # G列：BI指数
                    g_cell = ws.cell(row=row_idx, column=7)
                    g_cell.value = g_val
                    g_cell.number_format = '0.00'
                    
                    # H列：ADI密度
                    h_cell = ws.cell(row=row_idx, column=8)
                    h_cell.value = h_val
                    h_cell.number_format = '0.00'
                    
                    # I列：密度分级
                    i_cell = ws.cell(row=row_idx, column=9)
                    i_cell.value = grade
                    
                    processed_count += 1
                    
                    # 记录处理示例
                    if processed_count <= 10:
                        community = str(community_cell.value).strip() if community_cell.value else ""
                        calculation_log.append(
                            f"第{row_idx}行 {town}-{community}-{env_type}: "
                            f"户数:{d_val}, 阳性:{e_val}, 阳性率:{f_val}, "
                            f"BI:{g_val}, ADI:{h_val}, 等级:{grade}"
                        )
                
                except Exception as e:
                    print(f"更新第{row_idx}行时出错: {e}")
                    continue
                    
            except Exception as e:
                continue
        
        # 保存文件
        wb.save(file_path)
        
        print(f"✓ 成功处理 {processed_count} 行数据")
        
        # 显示处理示例
        if calculation_log:
            print("\n处理示例:")
            for log in calculation_log:
                print(f"   {log}")
        
        return True, processed_count
        
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        return False, 0

def comprehensive_analysis(file_path):
    """全面分析处理结果"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        # 统计数据
        grade_stats = {"0级": 0, "一级": 0, "二级": 0, "三级": 0}
        env_type_stats = {}
        total_households = 0
        total_positive = 0
        risk_examples = {"一级": [], "二级": [], "三级": []}
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                town = ws.cell(row=row_idx, column=1).value
                env_type = ws.cell(row=row_idx, column=3).value
                d_val = ws.cell(row=row_idx, column=4).value
                
                if not is_data_row(town, env_type, d_val):
                    continue
                
                # 获取数值
                d_val = safe_float_convert(d_val)
                e_val = safe_float_convert(ws.cell(row=row_idx, column=5).value)
                f_val = safe_float_convert(ws.cell(row=row_idx, column=6).value)
                g_val = safe_float_convert(ws.cell(row=row_idx, column=7).value)
                h_val = safe_float_convert(ws.cell(row=row_idx, column=8).value)
                grade = ws.cell(row=row_idx, column=9).value or "0级"
                
                # 统计总数
                total_households += d_val
                total_positive += e_val
                
                # 统计分级
                if grade in grade_stats:
                    grade_stats[grade] += 1
                
                # 统计环境类型
                env_str = str(env_type).strip()
                if env_str not in env_type_stats:
                    env_type_stats[env_str] = {"count": 0, "households": 0, "positive": 0}
                env_type_stats[env_str]["count"] += 1
                env_type_stats[env_str]["households"] += d_val
                env_type_stats[env_str]["positive"] += e_val
                
                # 收集高风险示例
                if grade in ["一级", "二级", "三级"] and len(risk_examples[grade]) < 3:
                    community = str(ws.cell(row=row_idx, column=2).value).strip() if ws.cell(row=row_idx, column=2).value else ""
                    risk_examples[grade].append(
                        f"{town}-{community}-{env_type} (BI:{g_val}, ADI:{h_val}, 阳性率:{f_val})"
                    )
                    
            except Exception as e:
                continue
        
        # 显示统计结果
        print(f"\n📊 完整统计结果:")
        print(f"=" * 50)
        
        # 密度分级统计
        total_areas = sum(grade_stats.values())
        if total_areas > 0:
            print(f"🏷️  蚊媒密度分级统计 (共{total_areas}个区域):")
            for grade, count in grade_stats.items():
                percentage = (count / total_areas * 100)
                color_map = {"0级": "🔵", "一级": "🟡", "二级": "🟠", "三级": "🔴"}
                print(f"   {color_map.get(grade, '⚪')} {grade}: {count} 个区域 ({percentage:.1f}%)")
        
        # 总体统计
        if total_households > 0:
            overall_rate = (total_positive / total_households) * 100
            print(f"\n📈 总体监测统计:")
            print(f"   • 总监测户数: {total_households:,.0f}")
            print(f"   • 总阳性数: {total_positive:,.0f}")
            print(f"   • 总体阳性率: {overall_rate:.2f}%")
        
        # 环境类型统计
        print(f"\n🏢 环境类型统计:")
        for env_type, stats in sorted(env_type_stats.items()):
            rate = (stats["positive"] / stats["households"] * 100) if stats["households"] > 0 else 0
            print(f"   • {env_type}: {stats['count']}个区域, "
                  f"户数:{stats['households']:,.0f}, "
                  f"阳性:{stats['positive']:,.0f}, "
                  f"阳性率:{rate:.2f}%")
        
        # 高风险区域示例
        for grade in ["三级", "二级", "一级"]:
            if risk_examples[grade]:
                risk_map = {"一级": "低度风险", "二级": "中度风险", "三级": "高度风险"}
                print(f"\n🚨 {grade}({risk_map[grade]})示例:")
                for example in risk_examples[grade]:
                    print(f"   • {example}")
        
        return grade_stats, env_type_stats
        
    except Exception as e:
        print(f"分析结果时出错: {e}")
        return None, None

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🎯 最终版格式化和计算Excel汇总表")
    print("=" * 60)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 2. 完整处理
        success, count = complete_format_and_calculate(file_path)
        
        if success and count > 0:
            print(f"\n🎉 格式化和计算完成！")
            
            # 3. 全面分析
            grade_stats, env_stats = comprehensive_analysis(file_path)
            
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            print(f"\n✅ 完成的操作:")
            print(f"   • D列：监测户数 - 格式化为数字型(0.00)")
            print(f"   • E列：阳性数 - 格式化为数字型(0.00)")
            print(f"   • F列：阳性率 - 计算公式 E/D×10，格式化为数字型(0.00)")
            print(f"   • G列：BI指数 - 格式化为数字型(0.00)")
            print(f"   • H列：ADI密度 - 格式化为数字型(0.00)")
            print(f"   • I列：密度分级 - 根据BI和ADI计算(0级/一级/二级/三级)")
            
        else:
            print(f"\n❌ 处理失败或没有找到有效数据")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    import os
    main()
