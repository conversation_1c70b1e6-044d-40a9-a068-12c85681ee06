#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正F列计算公式：F = E/D * 100
专门处理F列的阳性率计算
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_F列修正_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def is_valid_data_row(town, env_type, d_value):
    """判断是否是有效的数据行"""
    if not town or not env_type or d_value is None:
        return False
    
    town_str = str(town).strip()
    env_str = str(env_type).strip()
    
    # 跳过表头和汇总行
    if (town_str in ['镇街', ''] or 
        env_str in ['环境类型', ''] or
        '汇总' in town_str or '小计' in town_str):
        return False
    
    # 跳过公式行
    if isinstance(d_value, str) and d_value.startswith('='):
        return False
    
    return True

def safe_get_number(value):
    """安全获取数值"""
    if value is None:
        return 0
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        if value.startswith('='):
            return 0
        try:
            return float(value)
        except ValueError:
            return 0
    return 0

def fix_f_column_calculation(file_path):
    """修正F列计算：F = E/D * 100"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        updated_count = 0
        examples = []
        
        print("开始修正F列计算公式...")
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                # 读取基本信息
                town = ws.cell(row=row_idx, column=1).value
                env_type = ws.cell(row=row_idx, column=3).value
                d_value = ws.cell(row=row_idx, column=4).value
                
                # 检查是否是有效数据行
                if not is_valid_data_row(town, env_type, d_value):
                    continue
                
                # 获取D列和E列的数值
                d_val = safe_get_number(d_value)
                e_val = safe_get_number(ws.cell(row=row_idx, column=5).value)
                
                # 计算新的F列值：E/D * 100
                if d_val > 0:
                    new_f_val = round((e_val / d_val) * 100, 2)
                else:
                    new_f_val = 0.0
                
                # 更新F列
                f_cell = ws.cell(row=row_idx, column=6)
                old_f_val = safe_get_number(f_cell.value)
                
                f_cell.value = new_f_val
                f_cell.number_format = '0.00'
                
                updated_count += 1
                
                # 记录前10个更新示例
                if updated_count <= 10:
                    community = str(ws.cell(row=row_idx, column=2).value).strip() if ws.cell(row=row_idx, column=2).value else ""
                    examples.append(
                        f"第{row_idx}行 {town}-{community}-{env_type}: "
                        f"D={d_val}, E={e_val}, F={old_f_val}→{new_f_val}%"
                    )
                
            except Exception as e:
                continue
        
        # 保存文件
        wb.save(file_path)
        
        print(f"✓ 成功更新 {updated_count} 行的F列数据")
        
        # 显示更新示例
        if examples:
            print("\n更新示例:")
            for example in examples:
                print(f"   {example}")
        
        return True, updated_count
        
    except Exception as e:
        print(f"✗ 修正F列失败: {e}")
        return False, 0

def verify_f_column(file_path):
    """验证F列计算是否正确"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        correct_count = 0
        total_count = 0
        error_examples = []
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                town = ws.cell(row=row_idx, column=1).value
                env_type = ws.cell(row=row_idx, column=3).value
                d_value = ws.cell(row=row_idx, column=4).value
                
                if not is_valid_data_row(town, env_type, d_value):
                    continue
                
                d_val = safe_get_number(d_value)
                e_val = safe_get_number(ws.cell(row=row_idx, column=5).value)
                f_val = safe_get_number(ws.cell(row=row_idx, column=6).value)
                
                total_count += 1
                
                # 计算期望值
                if d_val > 0:
                    expected_f = round((e_val / d_val) * 100, 2)
                else:
                    expected_f = 0.0
                
                # 检查是否正确（允许小的浮点误差）
                if abs(f_val - expected_f) < 0.01:
                    correct_count += 1
                else:
                    if len(error_examples) < 5:
                        error_examples.append(
                            f"第{row_idx}行: D={d_val}, E={e_val}, F={f_val}, 期望={expected_f}"
                        )
                
            except Exception as e:
                continue
        
        print(f"\n📊 F列计算验证:")
        print(f"   • 总检查行数: {total_count}")
        print(f"   • 计算正确: {correct_count}")
        print(f"   • 计算错误: {total_count - correct_count}")
        print(f"   • 正确率: {(correct_count/total_count*100):.1f}%" if total_count > 0 else "   • 正确率: 0%")
        
        if error_examples:
            print(f"\n❌ 错误示例:")
            for example in error_examples:
                print(f"   {example}")
        
        return correct_count, total_count
        
    except Exception as e:
        print(f"验证F列时出错: {e}")
        return 0, 0

def show_sample_results(file_path):
    """显示一些计算结果示例"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        samples = []
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                town = ws.cell(row=row_idx, column=1).value
                env_type = ws.cell(row=row_idx, column=3).value
                d_value = ws.cell(row=row_idx, column=4).value
                
                if not is_valid_data_row(town, env_type, d_value):
                    continue
                
                d_val = safe_get_number(d_value)
                e_val = safe_get_number(ws.cell(row=row_idx, column=5).value)
                f_val = safe_get_number(ws.cell(row=row_idx, column=6).value)
                
                # 只收集有阳性数据的示例
                if e_val > 0 and len(samples) < 5:
                    community = str(ws.cell(row=row_idx, column=2).value).strip() if ws.cell(row=row_idx, column=2).value else ""
                    samples.append(
                        f"{town}-{community}-{env_type}: "
                        f"监测{d_val}户, 阳性{e_val}户, 阳性率{f_val}%"
                    )
                
            except Exception as e:
                continue
        
        if samples:
            print(f"\n📈 阳性率计算示例:")
            for sample in samples:
                print(f"   • {sample}")
        
    except Exception as e:
        print(f"显示示例时出错: {e}")

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🔧 修正F列计算公式：F = E/D × 100")
    print("=" * 50)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 2. 修正F列计算
        success, count = fix_f_column_calculation(file_path)
        
        if success and count > 0:
            print(f"\n🎉 F列修正完成！")
            
            # 3. 验证计算结果
            correct, total = verify_f_column(file_path)
            
            # 4. 显示示例
            show_sample_results(file_path)
            
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            print(f"\n✅ 修正说明:")
            print(f"   • F列公式已修改为：阳性率 = (E列/D列) × 100")
            print(f"   • 格式：保留2位小数")
            print(f"   • 单位：百分比(%)")
            
        else:
            print(f"\n❌ F列修正失败")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    import os
    main()
