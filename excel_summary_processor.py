#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门处理白云区登革热监测数据的Excel汇总脚本
从各个镇街的明细表汇总数据到汇总表
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import sys
import os

def read_detail_sheet(file_path, sheet_name):
    """读取单个明细表并提取关键数据"""
    try:
        # 读取原始数据
        df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        # 查找数据开始的行
        data_start_row = None
        for i, row in df_raw.iterrows():
            # 寻找包含"居委"、"环境类型"等关键词的行
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            if '居委' in row_str and '环境类型' in row_str:
                data_start_row = i
                break
        
        if data_start_row is None:
            print(f"在工作表 {sheet_name} 中未找到数据开始行")
            return None
        
        # 从数据开始行重新读取
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=data_start_row)
        
        # 清理数据
        df = df.dropna(how='all')  # 删除全空行
        
        # 添加镇街信息
        df['镇街'] = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
        
        print(f"从 {sheet_name} 提取数据: {df.shape[0]} 行")
        return df
        
    except Exception as e:
        print(f"读取工作表 {sheet_name} 时出错: {e}")
        return None

def extract_summary_data(detail_sheets_data):
    """从明细数据中提取汇总信息"""
    summary_data = []
    
    for sheet_name, df in detail_sheets_data.items():
        if df is None or df.empty:
            continue
            
        try:
            # 获取镇街名称
            town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
            
            # 查找包含数值的列
            numeric_cols = []
            for col in df.columns:
                if df[col].dtype in ['int64', 'float64', 'object']:
                    # 尝试转换为数值
                    try:
                        numeric_series = pd.to_numeric(df[col], errors='coerce')
                        if not numeric_series.isna().all():
                            numeric_cols.append(col)
                    except:
                        pass
            
            # 按居委分组汇总
            if len(df) > 0:
                # 查找居委列
                community_col = None
                for col in df.columns:
                    col_str = str(col).lower()
                    if '居委' in col_str or '村委' in col_str or col == df.columns[0]:
                        community_col = col
                        break
                
                if community_col is not None:
                    # 按居委分组
                    grouped = df.groupby(community_col)
                    
                    for community, group in grouped:
                        if pd.isna(community) or str(community).strip() == '':
                            continue
                            
                        row_data = {
                            '镇街': town_name,
                            '居委': str(community).strip(),
                            '记录数': len(group)
                        }
                        
                        # 汇总数值列
                        for col in numeric_cols:
                            try:
                                numeric_values = pd.to_numeric(group[col], errors='coerce')
                                valid_values = numeric_values.dropna()
                                if len(valid_values) > 0:
                                    row_data[f'{col}_总计'] = valid_values.sum()
                                    row_data[f'{col}_平均'] = valid_values.mean()
                                    row_data[f'{col}_最大'] = valid_values.max()
                            except:
                                pass
                        
                        summary_data.append(row_data)
                else:
                    # 如果没有找到居委列，按整个镇街汇总
                    row_data = {
                        '镇街': town_name,
                        '居委': '全镇街',
                        '记录数': len(df)
                    }
                    
                    for col in numeric_cols:
                        try:
                            numeric_values = pd.to_numeric(df[col], errors='coerce')
                            valid_values = numeric_values.dropna()
                            if len(valid_values) > 0:
                                row_data[f'{col}_总计'] = valid_values.sum()
                                row_data[f'{col}_平均'] = valid_values.mean()
                                row_data[f'{col}_最大'] = valid_values.max()
                        except:
                            pass
                    
                    summary_data.append(row_data)
                    
        except Exception as e:
            print(f"处理 {sheet_name} 数据时出错: {e}")
            continue
    
    return pd.DataFrame(summary_data)

def create_final_summary(summary_df):
    """创建最终的汇总表"""
    if summary_df.empty:
        return None
    
    # 按镇街进一步汇总
    town_summary = []
    
    for town in summary_df['镇街'].unique():
        town_data = summary_df[summary_df['镇街'] == town]
        
        row = {
            '镇街': town,
            '居委数量': len(town_data),
            '总记录数': town_data['记录数'].sum()
        }
        
        # 汇总数值列
        for col in town_data.columns:
            if col.endswith('_总计'):
                row[col] = town_data[col].sum()
            elif col.endswith('_平均'):
                row[col] = town_data[col].mean()
            elif col.endswith('_最大'):
                row[col] = town_data[col].max()
        
        town_summary.append(row)
    
    return pd.DataFrame(town_summary)

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    print(f"开始处理文件: {file_path}")
    
    # 获取所有工作表名称
    excel_file = pd.ExcelFile(file_path)
    sheet_names = excel_file.sheet_names
    
    # 识别明细表（排除汇总表和无用表）
    detail_sheets = [name for name in sheet_names 
                    if name != '汇总' and 'WpsReserved' not in name and name.strip() != '']
    
    print(f"发现明细表: {detail_sheets}")
    
    # 读取所有明细表
    detail_sheets_data = {}
    for sheet_name in detail_sheets:
        df = read_detail_sheet(file_path, sheet_name)
        if df is not None:
            detail_sheets_data[sheet_name] = df
    
    if not detail_sheets_data:
        print("没有成功读取任何明细表")
        return
    
    # 提取汇总数据
    print("\n开始提取汇总数据...")
    summary_df = extract_summary_data(detail_sheets_data)
    
    if summary_df.empty:
        print("未能提取到汇总数据")
        return
    
    print(f"提取到汇总数据: {len(summary_df)} 行")
    
    # 创建最终汇总
    final_summary = create_final_summary(summary_df)
    
    # 保存结果
    output_file = file_path.replace('.xlsx', '_汇总结果.xlsx')
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 详细汇总（按居委）
        summary_df.to_excel(writer, sheet_name='详细汇总', index=False)
        
        # 镇街汇总
        if final_summary is not None:
            final_summary.to_excel(writer, sheet_name='镇街汇总', index=False)
    
    print(f"\n汇总结果已保存到: {output_file}")
    
    # 显示预览
    print("\n=== 详细汇总预览 ===")
    print(summary_df.head(10))
    
    if final_summary is not None:
        print("\n=== 镇街汇总预览 ===")
        print(final_summary.head(10))
    
    print("\n处理完成!")

if __name__ == "__main__":
    main()
