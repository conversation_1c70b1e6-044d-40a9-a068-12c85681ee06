# 直接更新Excel汇总表(Sheet1)使用说明

## 🎯 功能概述
这个脚本可以直接读取Excel文件中各个镇街的明细表数据，并自动更新到汇总表(Sheet1)中，无需手动复制粘贴。

## 📁 文件说明

### 主要脚本
- **`final_update_sheet1.py`** - 推荐使用的主脚本（带备份和详细日志）
- `update_sheet1.py` - 基础版本

### 自动生成的文件
- `a1_备份_YYYYMMDD_HHMMSS.xlsx` - 自动备份的原文件
- 更新后的 `a1.xlsx` - 汇总表已被更新

## 🚀 使用方法

### 1. 准备工作
确保你的Excel文件命名为 `a1.xlsx` 并放在脚本同一目录下。

### 2. 运行脚本
```bash
python final_update_sheet1.py
```

### 3. 查看结果
- 脚本会自动备份原文件
- 直接更新 `a1.xlsx` 中的汇总表
- 在控制台显示更新进度和统计信息

## 📊 更新内容

脚本会更新汇总表中的以下列：
- **D列**: 监测户数
- **E列**: 阳性数  
- **F列**: 户外布雷图指数(BI)
- **G列**: 总雌蚊密度(雌性ADI)

## 🔍 匹配逻辑

脚本使用智能匹配算法：

1. **镇街匹配**: 
   - 支持模糊匹配（如"江高镇"匹配"江高"）
   - 自动处理"镇"、"街"等后缀

2. **居委匹配**:
   - 标准化名称（去除"居委会"、"村委会"等后缀）
   - 支持空居委的情况（匹配该镇街下所有环境类型）

3. **环境类型匹配**:
   - 精确匹配（居民区、学校、医疗机构等）

## 📈 处理结果示例

```
🔄 开始更新a1.xlsx的汇总表
============================================================
✓ 已创建备份文件: a1_备份_20250731_183845.xlsx

📊 从明细表提取数据...
   ✓ 江高镇: 处理了 168 条记录
   ✓ 人和镇: 处理了 31 条记录
   ✓ 太和镇: 处理了 26 条记录
   ...

✅ 成功提取 3437 条汇总记录

📝 更新汇总表...
   ✓ 第4行: 江高镇-神山居委-居民区 -> 户数:150, 阳性:0
   ✓ 第5行: 江高镇--公园景区 -> 户数:0, 阳性:0
   ...

🎉 汇总表更新完成！

📈 更新统计:
   • 处理记录数: 3437
   • 总监测户数: 65,195
   • 总阳性数: 1,384
   • 总体阳性率: 2.12%
```

## ⚠️ 注意事项

### 安全保障
- **自动备份**: 每次运行都会自动创建备份文件
- **错误处理**: 遇到错误时会保留原文件
- **回滚机制**: 可以从备份文件恢复

### 数据要求
- 保持原Excel文件的表头结构不变
- 不要修改工作表名称
- 确保明细表中的数值列包含有效数据

### 性能说明
- 处理24个镇街约需要10-30秒
- 大文件处理时间会相应增加
- 建议在处理前关闭Excel文件

## 🔧 自定义配置

如需修改匹配规则或更新列位置，可以编辑脚本中的以下部分：

```python
# 更新数据到对应列
ws.cell(row=row_idx, column=4, value=data['监测户数'])    # D列
ws.cell(row=row_idx, column=5, value=data['阳性数'])      # E列  
ws.cell(row=row_idx, column=6, value=data['平均布雷图指数']) # F列
ws.cell(row=row_idx, column=7, value=data['平均雌蚊密度'])  # G列
```

## 🆘 故障排除

### 常见问题

1. **"未找到汇总工作表"**
   - 确保Excel文件中有名为"汇总"的工作表

2. **"匹配失败"**
   - 检查镇街名称是否一致
   - 确认环境类型名称是否准确

3. **"数据为空"**
   - 检查明细表中是否有有效的数值数据
   - 确认表头结构是否正确

### 恢复方法
如果更新出现问题，可以：
1. 删除当前的 `a1.xlsx`
2. 将备份文件重命名为 `a1.xlsx`
3. 重新运行脚本

## 📞 技术支持

如遇到问题，请提供：
- 错误信息截图
- 原Excel文件的表头结构
- 期望的更新结果

---

**提示**: 建议在正式使用前，先用测试文件验证更新结果是否符合预期。
