# Excel数据汇总脚本使用说明

## 概述
这个脚本专门用于处理白云区登革热监测数据，从各个镇街的明细表中提取数据并生成汇总报告。

## 文件说明

### 主要脚本
- `correct_excel_processor.py` - **推荐使用的主要脚本**
- `excel_summary_processor.py` - 通用版本的处理脚本
- `data_explorer.py` - 数据结构探索脚本

### 生成的文件
- `白云区登革热监测数据汇总_最终版.xlsx` - **最终汇总结果**（推荐）
- `a1_汇总结果.xlsx` - 通用脚本生成的结果
- `白云区登革热监测数据汇总.xlsx` - 早期版本的结果

## 使用方法

### 1. 准备工作
确保你的Excel文件命名为 `a1.xlsx` 并放在脚本同一目录下。

### 2. 运行脚本
```bash
python correct_excel_processor.py
```

### 3. 查看结果
脚本会生成 `白云区登革热监测数据汇总_最终版.xlsx` 文件，包含三个工作表：

#### 镇街汇总表
包含以下字段：
- 镇街：镇街名称
- 居委数量：该镇街下的居委数量
- 监测点数量：监测点总数
- 监测户数_总计：总监测户数
- 阳性数_总计：总阳性数
- 阳性率(%)：阳性率百分比
- 平均布雷图指数：平均BI指数
- 平均雌蚊密度：平均ADI密度
- 最高布雷图指数：最高BI指数
- 最高雌蚊密度：最高ADI密度

#### 居委汇总表
按镇街和居委分组的详细汇总数据。

#### 详细数据表
包含所有原始监测数据的清理版本。

## 数据处理逻辑

### 数据提取
1. 从每个镇街的工作表中识别表头结构
2. 提取居委信息、环境类型、监测地址等基本信息
3. 提取监测户数、阳性数、布雷图指数、雌蚊密度等数值数据
4. 处理多个日期组的数据（每个工作表包含4个日期组的数据）

### 数据汇总
1. **镇街级汇总**：按镇街汇总所有监测数据
2. **居委级汇总**：按镇街和居委分组汇总
3. **计算指标**：
   - 阳性率 = 阳性数 / 监测户数 × 100%
   - 平均指数 = 所有非零值的平均数
   - 最高指数 = 所有值的最大值

## 处理结果示例

### 总体统计
- 处理镇街数量: 22个
- 总监测户数: 65,195户
- 总阳性数: 1,384
- 总体阳性率: 2.12%
- 平均布雷图指数: 3.47
- 平均雌蚊密度: 3.06

### 镇街排名（按阳性率）
1. 三元里街: 11.63%
2. 钟落潭镇: 5.31%
3. 太和镇: 3.62%
4. 棠景街: 2.66%
5. 人和镇: 2.02%

## 注意事项

1. **数据格式要求**：
   - Excel文件必须保持原有的表头结构
   - 不要修改工作表名称
   - 确保数值列包含有效的数字数据

2. **错误处理**：
   - 脚本会自动跳过空行和无效数据
   - 对于无法识别的数据会输出警告信息
   - 数值转换错误会被自动处理为0

3. **性能考虑**：
   - 处理大文件可能需要几分钟时间
   - 建议在处理前备份原始文件

## 自定义修改

如果需要修改脚本以适应不同的数据格式，主要修改以下部分：

1. **表头识别**：修改 `process_detail_sheet` 函数中的 `header_row` 和 `sub_header_row` 变量
2. **列位置**：调整数据提取时的列索引
3. **汇总字段**：在 `create_town_summary` 函数中添加或修改汇总字段

## 技术要求

- Python 3.6+
- pandas
- openpyxl
- numpy

安装依赖：
```bash
pip install pandas openpyxl numpy
```

## 联系支持

如果遇到问题或需要定制功能，请提供：
1. 错误信息截图
2. 原始Excel文件的结构说明
3. 期望的输出格式要求
