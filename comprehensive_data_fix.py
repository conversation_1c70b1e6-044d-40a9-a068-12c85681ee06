#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面数据完善脚本
重新从所有明细表提取真实数据，替换汇总表中的重复模板数据
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
from datetime import datetime
from collections import defaultdict

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_全面数据完善_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def normalize_name(name):
    """标准化名称"""
    if pd.isna(name) or name is None:
        return ""
    
    name_str = str(name).strip()
    name_str = name_str.replace('\n', '').replace('\r', '').strip()
    # 保留原始名称，只去除多余空格
    return name_str

def get_density_grade(bi_value, adi_value):
    """根据广州市蚊媒密度分级表确定密度等级"""
    bi = float(bi_value) if bi_value and not pd.isna(bi_value) else 0
    adi = float(adi_value) if adi_value and not pd.isna(adi_value) else 0
    
    if bi > 20 or adi > 10:
        return "三级"
    elif (10 < bi <= 20) or (5 < adi <= 10):
        return "二级"
    elif (5 < bi <= 10) or (2 < adi <= 5):
        return "一级"
    else:
        return "0级"

def extract_all_detail_data(file_path):
    """从所有明细表提取真实数据"""
    excel_file = pd.ExcelFile(file_path)
    detail_sheets = [name for name in excel_file.sheet_names 
                    if name != '汇总' and 'WpsReserved' not in name and name.strip() != '']
    
    all_data = defaultdict(lambda: {
        '监测户数': 0,
        '阳性数': 0,
        '布雷图指数_values': [],
        '雌蚊密度_values': []
    })
    
    processing_log = []
    
    print("开始从所有明细表提取数据...")
    
    for sheet_name in detail_sheets:
        try:
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
            
            current_community = None
            sheet_records = 0
            
            print(f"\n处理 {town_name}...")
            
            for i in range(len(df_raw)):
                row = df_raw.iloc[i]
                
                # 识别新的居委
                if pd.notna(row[0]) and str(row[0]).strip().isdigit():
                    community_info = str(row[1]).strip() if pd.notna(row[1]) else f"居委{row[0]}"
                    current_community = normalize_name(community_info)
                    if current_community:
                        print(f"  找到居委: {current_community}")
                
                # 处理环境类型数据
                if pd.notna(row[2]) and current_community:
                    env_type = str(row[2]).strip()
                    
                    if not env_type or env_type.lower() in ['nan', 'none']:
                        continue
                    
                    key = (town_name, current_community, env_type)
                    
                    # 检查多个可能的数据列组合
                    # 基于之前的分析，数据可能在不同的列组合中
                    data_found = False
                    
                    # 尝试不同的列组合
                    possible_columns = [
                        (5, 6, 7, 8),    # 列6-9
                        (10, 11, 12, 13), # 列11-14
                        (15, 16, 17, 18), # 列16-19
                        (20, 21, 22, 23), # 列21-24
                        (4, 5, 6, 7),     # 列5-8 (备选)
                    ]
                    
                    for h_col, p_col, bi_col, adi_col in possible_columns:
                        if len(row) > max(h_col, p_col, bi_col, adi_col):
                            households = pd.to_numeric(row[h_col], errors='coerce') if pd.notna(row[h_col]) else 0
                            positive = pd.to_numeric(row[p_col], errors='coerce') if pd.notna(row[p_col]) else 0
                            bi_index = pd.to_numeric(row[bi_col], errors='coerce') if pd.notna(row[bi_col]) else 0
                            adi_density = pd.to_numeric(row[adi_col], errors='coerce') if pd.notna(row[adi_col]) else 0
                            
                            # 处理NaN值
                            households = households if not pd.isna(households) else 0
                            positive = positive if not pd.isna(positive) else 0
                            bi_index = bi_index if not pd.isna(bi_index) else 0
                            adi_density = adi_density if not pd.isna(adi_density) else 0
                            
                            if households > 0 or positive > 0 or bi_index > 0 or adi_density > 0:
                                all_data[key]['监测户数'] += households
                                all_data[key]['阳性数'] += positive
                                
                                if bi_index > 0:
                                    all_data[key]['布雷图指数_values'].append(bi_index)
                                
                                if adi_density > 0:
                                    all_data[key]['雌蚊密度_values'].append(adi_density)
                                
                                data_found = True
                                sheet_records += 1
                                
                                # 显示前几个数据示例
                                if sheet_records <= 5:
                                    print(f"    {env_type}: 户数={households}, 阳性={positive}, BI={bi_index}, ADI={adi_density}")
            
            processing_log.append(f"✓ {town_name}: 处理了 {sheet_records} 条记录")
            
        except Exception as e:
            processing_log.append(f"✗ {sheet_name}: 处理失败 - {e}")
            continue
    
    # 计算平均值
    for key in all_data:
        data = all_data[key]
        
        if data['布雷图指数_values']:
            data['平均布雷图指数'] = round(np.mean(data['布雷图指数_values']), 4)
        else:
            data['平均布雷图指数'] = 0
            
        if data['雌蚊密度_values']:
            data['平均雌蚊密度'] = round(np.mean(data['雌蚊密度_values']), 4)
        else:
            data['平均雌蚊密度'] = 0
    
    return dict(all_data), processing_log

def update_summary_table_completely(file_path, extracted_data):
    """完全更新汇总表数据"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        update_log = []
        updated_count = 0
        matched_count = 0
        
        print("\n开始完全更新汇总表...")
        
        for row_idx in range(4, ws.max_row + 1):
            try:
                town_cell = ws.cell(row=row_idx, column=1)
                community_cell = ws.cell(row=row_idx, column=2)
                env_type_cell = ws.cell(row=row_idx, column=3)
                
                town = str(town_cell.value).strip() if town_cell.value else ""
                community = str(community_cell.value).strip() if community_cell.value else ""
                env_type = str(env_type_cell.value).strip() if env_type_cell.value else ""
                
                if not town or not env_type:
                    continue
                
                # 处理合并单元格的情况
                if community == "None" or not community:
                    # 向上查找最近的非空居委名称
                    for prev_row in range(row_idx - 1, 3, -1):
                        prev_community = ws.cell(row=prev_row, column=2).value
                        if prev_community and str(prev_community) != "None":
                            community = str(prev_community).strip()
                            break
                
                if not community:
                    continue
                
                # 查找匹配的数据
                matched_key = None
                best_match_score = 0
                
                for key in extracted_data:
                    key_town, key_community, key_env = key
                    
                    # 镇街匹配
                    town_match = (town in key_town or key_town in town or
                                town.replace('镇', '').replace('街', '') == 
                                key_town.replace('镇', '').replace('街', ''))
                    
                    # 居委匹配 - 更灵活的匹配
                    community_match = (community in key_community or 
                                     key_community in community or
                                     community.replace('居委会', '').replace('居委', '').replace('村委会', '').replace('村委', '') ==
                                     key_community.replace('居委会', '').replace('居委', '').replace('村委会', '').replace('村委', ''))
                    
                    # 环境类型精确匹配
                    env_match = (key_env == env_type)
                    
                    if town_match and community_match and env_match:
                        match_score = 1
                        if community == key_community:
                            match_score += 1
                        
                        if match_score > best_match_score:
                            best_match_score = match_score
                            matched_key = key
                
                if matched_key:
                    data = extracted_data[matched_key]
                    
                    # 更新数据
                    ws.cell(row=row_idx, column=4, value=data['监测户数'])
                    ws.cell(row=row_idx, column=5, value=data['阳性数'])
                    ws.cell(row=row_idx, column=7, value=data['平均布雷图指数'])
                    ws.cell(row=row_idx, column=8, value=data['平均雌蚊密度'])
                    
                    # 计算F列：阳性率 = E/D * 100
                    if data['监测户数'] > 0:
                        f_val = round((data['阳性数'] / data['监测户数']) * 100, 2)
                    else:
                        f_val = 0.0
                    ws.cell(row=row_idx, column=6, value=f_val)
                    
                    # 计算I列：密度分级
                    grade = get_density_grade(data['平均布雷图指数'], data['平均雌蚊密度'])
                    ws.cell(row=row_idx, column=9, value=grade)
                    
                    # 格式化数字列
                    for col in range(4, 9):
                        cell = ws.cell(row=row_idx, column=col)
                        if col != 9:  # 除了I列
                            cell.number_format = '0.00'
                    
                    matched_count += 1
                    
                    if matched_count <= 20:
                        update_log.append(f"✓ 第{row_idx}行: {town}-{community}-{env_type} -> "
                                        f"户数:{data['监测户数']}, 阳性:{data['阳性数']}, "
                                        f"阳性率:{f_val}%, BI:{data['平均布雷图指数']}, ADI:{data['平均雌蚊密度']}")
                else:
                    # 没有匹配到数据，设置为0
                    ws.cell(row=row_idx, column=4, value=0)
                    ws.cell(row=row_idx, column=5, value=0)
                    ws.cell(row=row_idx, column=6, value=0)
                    ws.cell(row=row_idx, column=7, value=0)
                    ws.cell(row=row_idx, column=8, value=0)
                    ws.cell(row=row_idx, column=9, value="0级")
                    
                    for col in range(4, 9):
                        cell = ws.cell(row=row_idx, column=col)
                        if col != 9:
                            cell.number_format = '0.00'
                
                updated_count += 1
                        
            except Exception as e:
                update_log.append(f"✗ 第{row_idx}行处理失败: {e}")
                continue
        
        # 保存文件
        wb.save(file_path)
        update_log.append(f"✓ 成功更新 {updated_count} 行，匹配到数据 {matched_count} 行")
        
        return True, update_log
        
    except Exception as e:
        return False, [f"✗ 更新失败: {e}"]

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🔧 全面数据完善 - 重新提取所有明细表数据")
    print("=" * 80)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 2. 从所有明细表提取真实数据
        extracted_data, processing_log = extract_all_detail_data(file_path)
        
        print("\n处理结果:")
        for log in processing_log:
            print(f"   {log}")
        
        if not extracted_data:
            print("❌ 未能提取到任何数据")
            return
        
        print(f"\n✅ 成功提取 {len(extracted_data)} 条汇总记录")
        
        # 3. 完全更新汇总表
        print("\n📝 完全更新汇总表...")
        success, update_log = update_summary_table_completely(file_path, extracted_data)
        
        print("\n更新结果:")
        for log in update_log[:15]:  # 显示前15条
            print(f"   {log}")
        
        if len(update_log) > 15:
            print(f"   ... 还有 {len(update_log) - 15} 条更新记录")
        
        if success:
            print("\n🎉 全面数据完善完成！")
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            print(f"\n✅ 完成的操作:")
            print(f"   • 从所有明细表重新提取真实数据")
            print(f"   • 替换汇总表中的重复模板数据")
            print(f"   • F列计算阳性率 = E/D×100")
            print(f"   • I列计算蚊媒密度分级")
            print(f"   • 所有数值格式化为保留2位小数")
            
        else:
            print("\n❌ 数据完善失败")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    import os
    main()
