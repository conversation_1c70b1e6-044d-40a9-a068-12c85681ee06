#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本的Excel数据处理脚本
专门处理白云区登革热监测数据，从明细表汇总到汇总表
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment, PatternFill
import sys
import os

def find_header_row(df):
    """查找表头行"""
    for i, row in df.iterrows():
        row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
        # 查找包含关键字段的行
        if any(keyword in row_str for keyword in ['居委', '环境类型', '监测户数', '阳性数', '布雷图指数']):
            return i
    return None

def clean_column_names(df):
    """清理列名"""
    new_columns = []
    for col in df.columns:
        col_str = str(col)
        if 'Unnamed' in col_str:
            new_columns.append(f'列{len(new_columns)+1}')
        else:
            # 清理列名中的换行符和多余空格
            clean_name = col_str.replace('\n', '').replace('\r', '').strip()
            new_columns.append(clean_name)
    df.columns = new_columns
    return df

def read_and_process_sheet(file_path, sheet_name):
    """读取并处理单个工作表"""
    try:
        # 读取原始数据
        df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        # 查找表头行
        header_row = find_header_row(df_raw)
        if header_row is None:
            print(f"在 {sheet_name} 中未找到有效的表头行")
            return None
        
        # 从表头行开始重新读取数据
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        
        # 清理列名
        df = clean_column_names(df)
        
        # 删除全空行
        df = df.dropna(how='all')
        
        # 添加镇街信息
        town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
        df['镇街'] = town_name
        
        print(f"成功处理 {sheet_name}: {len(df)} 行数据")
        return df
        
    except Exception as e:
        print(f"处理 {sheet_name} 时出错: {e}")
        return None

def extract_key_metrics(df):
    """提取关键指标"""
    metrics = {}
    
    # 查找关键列
    for col in df.columns:
        col_lower = str(col).lower()
        if '监测户数' in col_lower or '户数' in col_lower:
            metrics['监测户数'] = col
        elif '阳性数' in col_lower:
            metrics['阳性数'] = col
        elif '布雷图指数' in col_lower or 'bi' in col_lower:
            metrics['布雷图指数'] = col
        elif '雌蚊密度' in col_lower or 'adi' in col_lower:
            metrics['雌蚊密度'] = col
        elif '居委' in col_lower or '村委' in col_lower:
            metrics['居委'] = col
        elif '环境类型' in col_lower:
            metrics['环境类型'] = col
    
    return metrics

def create_summary_by_town(all_data):
    """按镇街创建汇总"""
    summary_data = []
    
    for town_name, df in all_data.items():
        if df is None or df.empty:
            continue
        
        # 提取关键指标列
        metrics = extract_key_metrics(df)
        
        # 基本统计
        row = {
            '镇街': town_name,
            '总记录数': len(df),
            '居委数量': 0,
            '监测户数_总计': 0,
            '阳性数_总计': 0,
            '平均布雷图指数': 0,
            '平均雌蚊密度': 0
        }
        
        # 统计居委数量
        if '居委' in metrics:
            unique_communities = df[metrics['居委']].dropna().nunique()
            row['居委数量'] = unique_communities
        
        # 统计监测户数
        if '监测户数' in metrics:
            try:
                households = pd.to_numeric(df[metrics['监测户数']], errors='coerce')
                row['监测户数_总计'] = households.sum()
            except:
                pass
        
        # 统计阳性数
        if '阳性数' in metrics:
            try:
                positive = pd.to_numeric(df[metrics['阳性数']], errors='coerce')
                row['阳性数_总计'] = positive.sum()
            except:
                pass
        
        # 计算平均布雷图指数
        if '布雷图指数' in metrics:
            try:
                bi_values = pd.to_numeric(df[metrics['布雷图指数']], errors='coerce')
                valid_bi = bi_values.dropna()
                if len(valid_bi) > 0:
                    row['平均布雷图指数'] = round(valid_bi.mean(), 4)
            except:
                pass
        
        # 计算平均雌蚊密度
        if '雌蚊密度' in metrics:
            try:
                adi_values = pd.to_numeric(df[metrics['雌蚊密度']], errors='coerce')
                valid_adi = adi_values.dropna()
                if len(valid_adi) > 0:
                    row['平均雌蚊密度'] = round(valid_adi.mean(), 4)
            except:
                pass
        
        # 计算阳性率
        if row['监测户数_总计'] > 0:
            row['阳性率(%)'] = round((row['阳性数_总计'] / row['监测户数_总计']) * 100, 2)
        else:
            row['阳性率(%)'] = 0
        
        summary_data.append(row)
    
    return pd.DataFrame(summary_data)

def create_detailed_summary(all_data):
    """创建详细汇总（按居委）"""
    detailed_data = []
    
    for town_name, df in all_data.items():
        if df is None or df.empty:
            continue
        
        metrics = extract_key_metrics(df)
        
        if '居委' not in metrics:
            continue
        
        # 按居委分组
        community_col = metrics['居委']
        grouped = df.groupby(community_col)
        
        for community, group in grouped:
            if pd.isna(community) or str(community).strip() == '':
                continue
            
            row = {
                '镇街': town_name,
                '居委': str(community).strip(),
                '记录数': len(group)
            }
            
            # 统计各项指标
            for metric_name, col_name in metrics.items():
                if metric_name in ['居委', '环境类型', '镇街']:
                    continue
                
                try:
                    values = pd.to_numeric(group[col_name], errors='coerce')
                    valid_values = values.dropna()
                    
                    if len(valid_values) > 0:
                        row[f'{metric_name}_总计'] = valid_values.sum()
                        row[f'{metric_name}_平均'] = round(valid_values.mean(), 4)
                        row[f'{metric_name}_最大'] = valid_values.max()
                except:
                    pass
            
            detailed_data.append(row)
    
    return pd.DataFrame(detailed_data)

def save_to_excel_with_formatting(summary_df, detailed_df, output_file):
    """保存到Excel并添加格式"""
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存镇街汇总
        summary_df.to_excel(writer, sheet_name='镇街汇总', index=False)
        
        # 保存详细汇总
        detailed_df.to_excel(writer, sheet_name='居委详细汇总', index=False)
        
        # 格式化镇街汇总表
        ws1 = writer.sheets['镇街汇总']
        
        # 设置表头格式
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        header_font = Font(color='FFFFFF', bold=True)
        
        for cell in ws1[1]:
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center')
        
        # 自动调整列宽
        for column in ws1.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws1.column_dimensions[column_letter].width = adjusted_width

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    print(f"开始处理文件: {file_path}")
    print("=" * 50)
    
    # 获取所有工作表
    excel_file = pd.ExcelFile(file_path)
    sheet_names = [name for name in excel_file.sheet_names 
                  if name != '汇总' and 'WpsReserved' not in name and name.strip() != '']
    
    print(f"发现 {len(sheet_names)} 个明细表")
    
    # 读取所有明细表
    all_data = {}
    for sheet_name in sheet_names:
        df = read_and_process_sheet(file_path, sheet_name)
        if df is not None:
            town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
            all_data[town_name] = df
    
    print(f"\n成功读取 {len(all_data)} 个工作表")
    print("=" * 50)
    
    # 创建汇总数据
    print("正在创建镇街汇总...")
    summary_df = create_summary_by_town(all_data)
    
    print("正在创建详细汇总...")
    detailed_df = create_detailed_summary(all_data)
    
    # 保存结果
    output_file = "白云区登革热监测数据汇总.xlsx"
    save_to_excel_with_formatting(summary_df, detailed_df, output_file)
    
    print(f"\n汇总完成！结果已保存到: {output_file}")
    print("=" * 50)
    
    # 显示汇总统计
    print("\n=== 镇街汇总统计 ===")
    print(f"共处理 {len(summary_df)} 个镇街")
    print(f"总监测户数: {summary_df['监测户数_总计'].sum():,}")
    print(f"总阳性数: {summary_df['阳性数_总计'].sum()}")
    print(f"总体阳性率: {(summary_df['阳性数_总计'].sum() / summary_df['监测户数_总计'].sum() * 100):.2f}%")
    
    print("\n=== 镇街汇总预览 ===")
    print(summary_df.head(10).to_string(index=False))
    
    print("\n处理完成！")

if __name__ == "__main__":
    main()
