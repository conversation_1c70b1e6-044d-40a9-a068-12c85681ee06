#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版格式化和计算Excel汇总表
专门处理数据行，避免合并单元格问题
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_简化格式化_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def get_density_grade(bi_value, adi_value):
    """根据广州市蚊媒密度分级表确定密度等级"""
    if pd.isna(bi_value) or pd.isna(adi_value):
        return "0级"
    
    bi = float(bi_value) if bi_value != 0 else 0
    adi = float(adi_value) if adi_value != 0 else 0
    
    # 三级（红色）：BI>20 或 ADI>10
    if bi > 20 or adi > 10:
        return "三级"
    # 二级（橙色）：10<BI≤20 或 5<ADI≤10  
    elif (10 < bi <= 20) or (5 < adi <= 10):
        return "二级"
    # 一级（黄色）：5<BI≤10 或 2<ADI≤5
    elif (5 < bi <= 10) or (2 < adi <= 5):
        return "一级"
    # 0级（蓝色）：0≤BI≤5 或 0≤ADI≤2
    else:
        return "0级"

def process_data_rows_only(file_path):
    """只处理纯数据行，避免合并单元格"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        processed_count = 0
        calculation_log = []
        
        print("开始处理数据行...")
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                # 读取关键列
                town_cell = ws.cell(row=row_idx, column=1)
                community_cell = ws.cell(row=row_idx, column=2)
                env_cell = ws.cell(row=row_idx, column=3)
                d_cell = ws.cell(row=row_idx, column=4)  # 监测户数
                e_cell = ws.cell(row=row_idx, column=5)  # 阳性数
                
                # 检查是否是有效的数据行
                town = str(town_cell.value).strip() if town_cell.value else ""
                env_type = str(env_cell.value).strip() if env_cell.value else ""
                
                # 跳过表头、空行、汇总行
                if (not town or not env_type or 
                    town in ['镇街', ''] or 
                    env_type in ['环境类型', ''] or
                    '汇总' in town or '小计' in town):
                    continue
                
                # 检查D列是否有数值（避免处理合并单元格）
                if d_cell.value is None:
                    continue
                
                # 如果D列是公式，跳过（这通常是汇总行）
                if isinstance(d_cell.value, str) and d_cell.value.startswith('='):
                    continue
                
                # 获取数值
                try:
                    d_val = float(d_cell.value) if d_cell.value else 0
                    e_val = float(e_cell.value) if e_cell.value else 0
                except (ValueError, TypeError):
                    continue
                
                # 读取G、H列（BI和ADI）
                g_cell = ws.cell(row=row_idx, column=7)
                h_cell = ws.cell(row=row_idx, column=8)
                
                try:
                    g_val = float(g_cell.value) if g_cell.value and not isinstance(g_cell.value, str) else 0
                    h_val = float(h_cell.value) if h_cell.value and not isinstance(h_cell.value, str) else 0
                except (ValueError, TypeError):
                    g_val = 0
                    h_val = 0
                
                # 计算F列：阳性率 = E列/D列 * 10
                if d_val > 0:
                    f_val = round((e_val / d_val) * 10, 2)
                else:
                    f_val = 0
                
                # 计算密度分级
                grade = get_density_grade(g_val, h_val)
                
                # 更新数据（只更新非合并单元格）
                try:
                    # 格式化D列
                    if not isinstance(d_cell.value, str):
                        d_cell.value = round(d_val, 2)
                        d_cell.number_format = '0.00'
                    
                    # 格式化E列
                    if not isinstance(e_cell.value, str):
                        e_cell.value = round(e_val, 2)
                        e_cell.number_format = '0.00'
                    
                    # 更新F列
                    f_cell = ws.cell(row=row_idx, column=6)
                    if not isinstance(f_cell.value, str) or not str(f_cell.value).startswith('='):
                        f_cell.value = f_val
                        f_cell.number_format = '0.00'
                    
                    # 格式化G列
                    if not isinstance(g_cell.value, str):
                        g_cell.value = round(g_val, 2)
                        g_cell.number_format = '0.00'
                    
                    # 格式化H列
                    if not isinstance(h_cell.value, str):
                        h_cell.value = round(h_val, 2)
                        h_cell.number_format = '0.00'
                    
                    # 更新I列（密度分级）
                    i_cell = ws.cell(row=row_idx, column=9)
                    i_cell.value = grade
                    
                    processed_count += 1
                    
                    # 记录前10个处理示例
                    if processed_count <= 10:
                        community = str(community_cell.value).strip() if community_cell.value else ""
                        calculation_log.append(
                            f"第{row_idx}行 {town}-{community}-{env_type}: "
                            f"户数:{d_val}, 阳性:{e_val}, 阳性率:{f_val}, "
                            f"BI:{g_val}, ADI:{h_val}, 等级:{grade}"
                        )
                
                except Exception as e:
                    # 如果是合并单元格，跳过
                    continue
                    
            except Exception as e:
                continue
        
        # 保存文件
        wb.save(file_path)
        
        print(f"✓ 成功处理 {processed_count} 行数据")
        
        # 显示处理示例
        if calculation_log:
            print("\n处理示例:")
            for log in calculation_log:
                print(f"   {log}")
        
        return True, processed_count
        
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        return False, 0

def analyze_results(file_path):
    """分析处理结果"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        grade_stats = {"0级": 0, "一级": 0, "二级": 0, "三级": 0}
        total_households = 0
        total_positive = 0
        risk_examples = {"一级": [], "二级": [], "三级": []}
        
        for row_idx in range(1, ws.max_row + 1):
            try:
                # 检查是否是数据行
                town_cell = ws.cell(row=row_idx, column=1)
                env_cell = ws.cell(row=row_idx, column=3)
                d_cell = ws.cell(row=row_idx, column=4)
                
                if (not town_cell.value or not env_cell.value or 
                    not d_cell.value or isinstance(d_cell.value, str)):
                    continue
                
                # 统计数据
                d_val = float(d_cell.value) if d_cell.value else 0
                e_val = float(ws.cell(row=row_idx, column=5).value) if ws.cell(row=row_idx, column=5).value else 0
                grade = ws.cell(row=row_idx, column=9).value
                
                if d_val > 0:
                    total_households += d_val
                    total_positive += e_val
                
                if grade in grade_stats:
                    grade_stats[grade] += 1
                
                # 收集高风险示例
                if grade in ["一级", "二级", "三级"] and len(risk_examples[grade]) < 3:
                    town = str(town_cell.value).strip()
                    community = str(ws.cell(row=row_idx, column=2).value).strip() if ws.cell(row=row_idx, column=2).value else ""
                    env_type = str(env_cell.value).strip()
                    bi_val = ws.cell(row=row_idx, column=7).value or 0
                    adi_val = ws.cell(row=row_idx, column=8).value or 0
                    
                    risk_examples[grade].append(
                        f"{town}-{community}-{env_type} (BI:{bi_val}, ADI:{adi_val})"
                    )
                    
            except Exception as e:
                continue
        
        # 显示统计结果
        print(f"\n📊 处理结果统计:")
        total = sum(grade_stats.values())
        if total > 0:
            for grade, count in grade_stats.items():
                percentage = (count / total * 100)
                color_map = {"0级": "🔵", "一级": "🟡", "二级": "🟠", "三级": "🔴"}
                print(f"   {color_map.get(grade, '⚪')} {grade}: {count} 个区域 ({percentage:.1f}%)")
        
        if total_households > 0:
            overall_rate = (total_positive / total_households) * 100
            print(f"\n📈 总体统计:")
            print(f"   • 总监测户数: {total_households:,.0f}")
            print(f"   • 总阳性数: {total_positive:,.0f}")
            print(f"   • 总体阳性率: {overall_rate:.2f}%")
        
        # 显示高风险区域
        for grade in ["三级", "二级", "一级"]:
            if risk_examples[grade]:
                risk_map = {"一级": "低度风险", "二级": "中度风险", "三级": "高度风险"}
                print(f"\n{grade}({risk_map[grade]})示例:")
                for example in risk_examples[grade]:
                    print(f"   • {example}")
        
        return grade_stats
        
    except Exception as e:
        print(f"分析结果时出错: {e}")
        return None

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🔧 简化版格式化和计算Excel汇总表")
    print("=" * 50)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 2. 处理数据
        success, count = process_data_rows_only(file_path)
        
        if success and count > 0:
            print(f"\n🎉 格式化和计算完成！")
            
            # 3. 分析结果
            analyze_results(file_path)
            
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            print(f"\n📋 完成的操作:")
            print(f"   • D-I列格式化为数字型（保留2位小数）")
            print(f"   • F列计算阳性率 = E列/D列 × 10")
            print(f"   • I列根据BI和ADI值计算蚊媒密度分级")
            
        else:
            print(f"\n❌ 处理失败或没有找到有效数据")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    import os
    main()
