#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理脚本
从明细表(sheet2-25)读取数据，汇总到汇总表(sheet1)
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import sys
import os

def read_excel_file(file_path):
    """读取Excel文件并返回所有sheet的数据"""
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        print(f"发现的工作表: {excel_file.sheet_names}")

        # 读取所有工作表
        sheets_data = {}
        for sheet_name in excel_file.sheet_names:
            try:
                # 跳过图片列表等无用工作表
                if 'WpsReserved' in sheet_name or sheet_name == '':
                    continue

                # 尝试不同的header行来读取数据
                df = None
                for header_row in [0, 1, 2, 3]:
                    try:
                        temp_df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
                        # 检查是否有有效的列名
                        if not all(col.startswith('Unnamed') for col in temp_df.columns if isinstance(col, str)):
                            df = temp_df
                            print(f"使用第{header_row}行作为表头")
                            break
                    except:
                        continue

                # 如果没有找到合适的表头，使用默认方式读取
                if df is None:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                    print(f"使用无表头方式读取")

                sheets_data[sheet_name] = df
                print(f"成功读取工作表 '{sheet_name}': {df.shape[0]} 行, {df.shape[1]} 列")
            except Exception as e:
                print(f"读取工作表 '{sheet_name}' 时出错: {e}")

        return sheets_data
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def analyze_sheets_structure(sheets_data):
    """分析工作表结构"""
    print("\n=== 工作表结构分析 ===")
    
    for sheet_name, df in sheets_data.items():
        print(f"\n工作表: {sheet_name}")
        print(f"形状: {df.shape}")
        print("列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i}: {col}")
        
        # 显示前几行数据
        print("前5行数据:")
        print(df.head())
        print("-" * 50)

def identify_detail_and_summary_sheets(sheets_data):
    """识别明细表和汇总表"""
    detail_sheets = []
    summary_sheet = None
    
    for sheet_name, df in sheets_data.items():
        # 根据工作表名称和数据量判断
        if 'sheet1' in sheet_name.lower() or '汇总' in sheet_name:
            summary_sheet = sheet_name
        elif df.shape[0] > 10:  # 假设明细表有更多行数据
            detail_sheets.append(sheet_name)
    
    print(f"\n识别结果:")
    print(f"汇总表: {summary_sheet}")
    print(f"明细表: {detail_sheets}")
    
    return detail_sheets, summary_sheet

def process_detail_data(sheets_data, detail_sheets):
    """处理明细表数据"""
    all_detail_data = []
    
    for sheet_name in detail_sheets:
        df = sheets_data[sheet_name]
        print(f"\n处理明细表: {sheet_name}")
        
        # 清理数据
        df_clean = df.dropna(how='all')  # 删除全空行
        df_clean = df_clean.dropna(how='all', axis=1)  # 删除全空列
        
        # 添加来源工作表标识
        df_clean['来源工作表'] = sheet_name
        
        all_detail_data.append(df_clean)
        print(f"清理后数据: {df_clean.shape[0]} 行, {df_clean.shape[1]} 列")
    
    # 合并所有明细数据
    if all_detail_data:
        combined_detail = pd.concat(all_detail_data, ignore_index=True)
        print(f"\n合并后的明细数据: {combined_detail.shape[0]} 行, {combined_detail.shape[1]} 列")
        return combined_detail
    else:
        return None

def create_summary_data(detail_data):
    """根据明细数据创建汇总数据"""
    if detail_data is None or detail_data.empty:
        print("没有明细数据可以汇总")
        return None
    
    print("\n=== 创建汇总数据 ===")
    
    # 显示明细数据的列名，帮助用户了解数据结构
    print("明细数据列名:")
    for i, col in enumerate(detail_data.columns):
        print(f"  {i}: {col}")
    
    # 尝试识别关键列
    # 这里需要根据实际数据结构调整
    group_columns = []
    numeric_columns = []
    
    for col in detail_data.columns:
        col_str = str(col).lower()
        # 识别分组列（村、镇、委等）
        if any(keyword in col_str for keyword in ['村', '镇', '委', '地区', '区域']):
            group_columns.append(col)
        # 识别数值列
        elif detail_data[col].dtype in ['int64', 'float64']:
            numeric_columns.append(col)
    
    print(f"识别的分组列: {group_columns}")
    print(f"识别的数值列: {numeric_columns}")
    
    if not group_columns:
        print("未能识别分组列，使用默认分组方式")
        # 如果没有识别到分组列，尝试使用第一列作为分组列
        if len(detail_data.columns) > 0:
            group_columns = [detail_data.columns[0]]
    
    # 创建汇总数据
    if group_columns and numeric_columns:
        try:
            summary_data = detail_data.groupby(group_columns)[numeric_columns].agg({
                col: ['sum', 'count', 'mean'] for col in numeric_columns
            }).round(2)
            
            # 展平多级列名
            summary_data.columns = ['_'.join(col).strip() for col in summary_data.columns.values]
            summary_data = summary_data.reset_index()
            
            print(f"汇总数据创建成功: {summary_data.shape[0]} 行, {summary_data.shape[1]} 列")
            return summary_data
        except Exception as e:
            print(f"创建汇总数据时出错: {e}")
            return None
    else:
        print("无法创建汇总数据：缺少必要的分组列或数值列")
        return None

def write_summary_to_excel(file_path, summary_data, summary_sheet_name='Sheet1'):
    """将汇总数据写入Excel文件"""
    try:
        # 加载现有的Excel文件
        book = load_workbook(file_path)
        
        # 如果汇总表存在，删除它
        if summary_sheet_name in book.sheetnames:
            del book[summary_sheet_name]
        
        # 创建新的汇总表
        ws = book.create_sheet(summary_sheet_name, 0)  # 插入到第一个位置
        
        # 写入数据
        for r_idx, row in enumerate(summary_data.values, 1):
            for c_idx, value in enumerate(row, 1):
                ws.cell(row=r_idx + 1, column=c_idx, value=value)
        
        # 写入列标题
        for c_idx, col_name in enumerate(summary_data.columns, 1):
            ws.cell(row=1, column=c_idx, value=col_name)
        
        # 保存文件
        book.save(file_path)
        print(f"汇总数据已写入到 {summary_sheet_name} 工作表")
        
    except Exception as e:
        print(f"写入Excel文件时出错: {e}")

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    print(f"开始处理文件: {file_path}")
    
    # 1. 读取Excel文件
    sheets_data = read_excel_file(file_path)
    if not sheets_data:
        return
    
    # 2. 分析工作表结构
    analyze_sheets_structure(sheets_data)
    
    # 3. 识别明细表和汇总表
    detail_sheets, summary_sheet = identify_detail_and_summary_sheets(sheets_data)
    
    if not detail_sheets:
        print("未找到明细表")
        return
    
    # 4. 处理明细数据
    detail_data = process_detail_data(sheets_data, detail_sheets)
    
    # 5. 创建汇总数据
    summary_data = create_summary_data(detail_data)
    
    if summary_data is not None:
        # 6. 显示汇总结果
        print("\n=== 汇总结果预览 ===")
        print(summary_data.head(10))
        
        # 7. 写入Excel文件
        output_file = file_path.replace('.xlsx', '_processed.xlsx')
        try:
            summary_data.to_excel(output_file, sheet_name='汇总表', index=False)
            print(f"\n汇总数据已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    
    print("\n处理完成!")

if __name__ == "__main__":
    main()
