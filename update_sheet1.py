#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接更新a1.xlsx的Sheet1汇总表
从明细表中提取数据并填入到汇总表的对应位置
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import sys
import os

def read_summary_template(file_path):
    """读取汇总表模板，了解其结构"""
    try:
        df_summary = pd.read_excel(file_path, sheet_name='汇总', header=None)
        print("汇总表结构分析:")
        print(f"形状: {df_summary.shape}")
        
        # 查找表头行
        for i in range(min(10, len(df_summary))):
            row_str = ' | '.join([str(cell)[:15] if pd.notna(cell) else 'NaN' for cell in df_summary.iloc[i]])
            print(f"第{i}行: {row_str}")
        
        return df_summary
    except Exception as e:
        print(f"读取汇总表时出错: {e}")
        return None

def extract_data_from_detail_sheets(file_path):
    """从明细表中提取数据"""
    excel_file = pd.ExcelFile(file_path)
    detail_sheets = [name for name in excel_file.sheet_names 
                    if name != '汇总' and 'WpsReserved' not in name and name.strip() != '']
    
    # 存储按镇街和居委分组的数据
    summary_data = {}
    
    for sheet_name in detail_sheets:
        try:
            # 读取明细表
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
            
            print(f"处理 {town_name}...")
            
            current_community = None
            
            for i in range(5, len(df_raw)):
                row = df_raw.iloc[i]
                
                # 检查是否是新的居委
                if pd.notna(row[0]) and str(row[0]).strip().isdigit():
                    community_info = str(row[1]).strip() if pd.notna(row[1]) else f"居委{row[0]}"
                    current_community = community_info.replace('\n', '').strip()
                
                # 检查是否有环境类型数据
                if pd.notna(row[2]) and current_community:
                    env_type = str(row[2]).strip()
                    
                    # 创建唯一键
                    key = (town_name, current_community, env_type)
                    
                    if key not in summary_data:
                        summary_data[key] = {
                            '监测户数': 0,
                            '阳性数': 0,
                            '布雷图指数_sum': 0,
                            '布雷图指数_count': 0,
                            '雌蚊密度_sum': 0,
                            '雌蚊密度_count': 0
                        }
                    
                    # 提取4个日期组的数据
                    for col_group in range(4):
                        base_col = 4 + col_group * 5
                        
                        if base_col + 4 < len(row):
                            households = pd.to_numeric(row[base_col + 1], errors='coerce') or 0
                            positive = pd.to_numeric(row[base_col + 2], errors='coerce') or 0
                            bi_index = pd.to_numeric(row[base_col + 3], errors='coerce') or 0
                            adi_density = pd.to_numeric(row[base_col + 4], errors='coerce') or 0
                            
                            if households > 0 or positive > 0 or bi_index > 0 or adi_density > 0:
                                summary_data[key]['监测户数'] += households
                                summary_data[key]['阳性数'] += positive
                                
                                if bi_index > 0:
                                    summary_data[key]['布雷图指数_sum'] += bi_index
                                    summary_data[key]['布雷图指数_count'] += 1
                                
                                if adi_density > 0:
                                    summary_data[key]['雌蚊密度_sum'] += adi_density
                                    summary_data[key]['雌蚊密度_count'] += 1
                                    
        except Exception as e:
            print(f"处理 {sheet_name} 时出错: {e}")
            continue
    
    # 计算平均值
    for key in summary_data:
        data = summary_data[key]
        if data['布雷图指数_count'] > 0:
            data['平均布雷图指数'] = round(data['布雷图指数_sum'] / data['布雷图指数_count'], 4)
        else:
            data['平均布雷图指数'] = 0
            
        if data['雌蚊密度_count'] > 0:
            data['平均雌蚊密度'] = round(data['雌蚊密度_sum'] / data['雌蚊密度_count'], 4)
        else:
            data['平均雌蚊密度'] = 0
    
    print(f"提取到 {len(summary_data)} 条汇总记录")
    return summary_data

def update_summary_sheet(file_path, summary_data):
    """更新汇总表"""
    try:
        # 使用openpyxl加载工作簿
        wb = load_workbook(file_path)
        
        # 获取汇总工作表
        if '汇总' in wb.sheetnames:
            ws = wb['汇总']
        else:
            print("未找到'汇总'工作表")
            return False
        
        print("开始更新汇总表...")
        
        # 遍历汇总表的每一行，查找匹配的数据
        updated_count = 0
        
        for row_idx in range(3, ws.max_row + 1):  # 从第3行开始（跳过表头）
            try:
                # 读取镇街、居委、环境类型
                town_cell = ws.cell(row=row_idx, column=1)  # A列：镇街
                community_cell = ws.cell(row=row_idx, column=2)  # B列：居委
                env_type_cell = ws.cell(row=row_idx, column=3)  # C列：环境类型
                
                town = str(town_cell.value).strip() if town_cell.value else ""
                community = str(community_cell.value).strip() if community_cell.value else ""
                env_type = str(env_type_cell.value).strip() if env_type_cell.value else ""
                
                # 清理镇街名称（去掉"镇"、"街"等后缀进行匹配）
                town_clean = town.replace('镇', '').replace('街', '').strip()
                community_clean = community.replace('\n', '').strip()
                
                # 查找匹配的数据
                matched_key = None
                for key in summary_data:
                    key_town, key_community, key_env = key
                    key_town_clean = key_town.replace('镇', '').replace('街', '').strip()
                    key_community_clean = key_community.replace('\n', '').strip()
                    
                    if (key_town_clean == town_clean and 
                        key_community_clean == community_clean and 
                        key_env == env_type):
                        matched_key = key
                        break
                
                if matched_key:
                    data = summary_data[matched_key]
                    
                    # 更新数据到对应列
                    # 假设列结构：A=镇街, B=居委, C=环境类型, D=监测户数, E=阳性数, F=BI, G=ADI
                    ws.cell(row=row_idx, column=4, value=data['监测户数'])  # D列：监测户数
                    ws.cell(row=row_idx, column=5, value=data['阳性数'])    # E列：阳性数
                    ws.cell(row=row_idx, column=6, value=data['平均布雷图指数'])  # F列：BI
                    ws.cell(row=row_idx, column=7, value=data['平均雌蚊密度'])   # G列：ADI
                    
                    updated_count += 1
                    
                    if updated_count <= 10:  # 只打印前10个更新的记录
                        print(f"更新: {town}-{community}-{env_type} -> 户数:{data['监测户数']}, 阳性:{data['阳性数']}")
                        
            except Exception as e:
                print(f"处理第{row_idx}行时出错: {e}")
                continue
        
        # 保存文件
        wb.save(file_path)
        print(f"成功更新 {updated_count} 行数据到汇总表")
        return True
        
    except Exception as e:
        print(f"更新汇总表时出错: {e}")
        return False

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    print("开始更新a1.xlsx的汇总表")
    print("=" * 50)
    
    # 1. 读取汇总表结构
    summary_template = read_summary_template(file_path)
    if summary_template is None:
        return
    
    print("\n" + "=" * 50)
    
    # 2. 从明细表提取数据
    summary_data = extract_data_from_detail_sheets(file_path)
    if not summary_data:
        print("未能提取到任何数据")
        return
    
    print("\n" + "=" * 50)
    
    # 3. 更新汇总表
    success = update_summary_sheet(file_path, summary_data)
    
    if success:
        print("\n汇总表更新完成！")
        print("请打开a1.xlsx查看汇总表的更新结果")
    else:
        print("\n汇总表更新失败")
    
    # 4. 显示统计信息
    total_households = 0
    total_positive = 0

    for data in summary_data.values():
        if not pd.isna(data['监测户数']):
            total_households += data['监测户数']
        if not pd.isna(data['阳性数']):
            total_positive += data['阳性数']

    print(f"\n=== 更新统计 ===")
    print(f"处理记录数: {len(summary_data)}")
    print(f"总监测户数: {total_households}")
    print(f"总阳性数: {total_positive}")
    if total_households > 0:
        print(f"总体阳性率: {(total_positive/total_households*100):.2f}%")

if __name__ == "__main__":
    main()
