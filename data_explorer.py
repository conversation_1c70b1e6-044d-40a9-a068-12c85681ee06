#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索脚本 - 用于理解Excel文件的数据结构
"""

import pandas as pd
import numpy as np

def explore_sheet(file_path, sheet_name, max_rows=20):
    """探索单个工作表的结构"""
    print(f"\n{'='*60}")
    print(f"探索工作表: {sheet_name}")
    print(f"{'='*60}")
    
    try:
        # 尝试不同的方式读取数据
        print("方法1: 默认读取")
        df1 = pd.read_excel(file_path, sheet_name=sheet_name)
        print(f"形状: {df1.shape}")
        print("前5行:")
        print(df1.head())
        
        print("\n方法2: 无表头读取")
        df2 = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        print(f"形状: {df2.shape}")
        print("前10行:")
        print(df2.head(10))
        
        # 查找包含关键词的行
        print("\n查找包含关键词的行:")
        keywords = ['居委', '环境类型', '监测户数', '阳性数', '布雷图指数', '雌蚊密度']
        
        for i, row in df2.head(20).iterrows():
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            for keyword in keywords:
                if keyword in row_str:
                    print(f"第{i}行包含'{keyword}': {row_str[:100]}...")
                    break
        
        # 查找数值列
        print("\n数值列分析:")
        for col in df1.columns:
            try:
                numeric_data = pd.to_numeric(df1[col], errors='coerce')
                non_null_count = numeric_data.count()
                if non_null_count > 0:
                    print(f"列 '{col}': {non_null_count} 个数值, 范围: {numeric_data.min():.2f} - {numeric_data.max():.2f}")
            except:
                pass
                
    except Exception as e:
        print(f"读取工作表时出错: {e}")

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    # 获取工作表列表
    excel_file = pd.ExcelFile(file_path)
    sheet_names = excel_file.sheet_names
    
    print(f"发现的工作表: {sheet_names}")
    
    # 先探索汇总表
    if '汇总' in sheet_names:
        explore_sheet(file_path, '汇总')
    
    # 探索几个明细表
    detail_sheets = [name for name in sheet_names if name != '汇总' and 'WpsReserved' not in name][:3]
    
    for sheet_name in detail_sheets:
        explore_sheet(file_path, sheet_name)
        
        # 询问是否继续
        response = input(f"\n是否继续探索下一个工作表? (y/n): ")
        if response.lower() != 'y':
            break

if __name__ == "__main__":
    main()
