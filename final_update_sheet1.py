#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本：直接更新a1.xlsx的Sheet1汇总表
- 自动备份原文件
- 智能匹配镇街和居委名称
- 详细的更新日志
- 错误处理和回滚功能
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
import shutil
import sys
import os
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_备份_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def normalize_name(name):
    """标准化名称，用于匹配"""
    if pd.isna(name) or name is None:
        return ""
    
    name_str = str(name).strip()
    # 移除换行符和多余空格
    name_str = name_str.replace('\n', '').replace('\r', '').strip()
    # 移除常见后缀
    name_str = name_str.replace('居委会', '').replace('居委', '').replace('村委会', '').replace('村委', '')
    name_str = name_str.replace('村', '').replace('社区', '').strip()
    
    return name_str

def extract_summary_data(file_path):
    """从明细表提取汇总数据"""
    excel_file = pd.ExcelFile(file_path)
    detail_sheets = [name for name in excel_file.sheet_names 
                    if name != '汇总' and 'WpsReserved' not in name and name.strip() != '']
    
    summary_data = {}
    processing_log = []
    
    for sheet_name in detail_sheets:
        try:
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            town_name = sheet_name.split('.')[1] if '.' in sheet_name else sheet_name
            
            current_community = None
            records_count = 0
            
            for i in range(5, len(df_raw)):
                row = df_raw.iloc[i]
                
                # 识别新的居委
                if pd.notna(row[0]) and str(row[0]).strip().isdigit():
                    community_info = str(row[1]).strip() if pd.notna(row[1]) else f"居委{row[0]}"
                    current_community = normalize_name(community_info)
                
                # 处理环境类型数据
                if pd.notna(row[2]) and current_community:
                    env_type = str(row[2]).strip()
                    
                    # 跳过空的环境类型
                    if not env_type or env_type.lower() in ['nan', 'none']:
                        continue
                    
                    key = (town_name, current_community, env_type)
                    
                    if key not in summary_data:
                        summary_data[key] = {
                            '监测户数': 0,
                            '阳性数': 0,
                            '布雷图指数_values': [],
                            '雌蚊密度_values': []
                        }
                    
                    # 提取4个日期组的数据
                    for col_group in range(4):
                        base_col = 4 + col_group * 5
                        
                        if base_col + 4 < len(row):
                            households = pd.to_numeric(row[base_col + 1], errors='coerce') or 0
                            positive = pd.to_numeric(row[base_col + 2], errors='coerce') or 0
                            bi_index = pd.to_numeric(row[base_col + 3], errors='coerce') or 0
                            adi_density = pd.to_numeric(row[base_col + 4], errors='coerce') or 0
                            
                            if households > 0 or positive > 0 or bi_index > 0 or adi_density > 0:
                                summary_data[key]['监测户数'] += households
                                summary_data[key]['阳性数'] += positive
                                
                                if bi_index > 0:
                                    summary_data[key]['布雷图指数_values'].append(bi_index)
                                
                                if adi_density > 0:
                                    summary_data[key]['雌蚊密度_values'].append(adi_density)
                                
                                records_count += 1
            
            processing_log.append(f"✓ {town_name}: 处理了 {records_count} 条记录")
            
        except Exception as e:
            processing_log.append(f"✗ {sheet_name}: 处理失败 - {e}")
            continue
    
    # 计算平均值
    for key in summary_data:
        data = summary_data[key]
        
        if data['布雷图指数_values']:
            data['平均布雷图指数'] = round(np.mean(data['布雷图指数_values']), 4)
        else:
            data['平均布雷图指数'] = 0
            
        if data['雌蚊密度_values']:
            data['平均雌蚊密度'] = round(np.mean(data['雌蚊密度_values']), 4)
        else:
            data['平均雌蚊密度'] = 0
    
    return summary_data, processing_log

def update_summary_sheet(file_path, summary_data):
    """更新汇总表"""
    try:
        wb = load_workbook(file_path)
        
        if '汇总' not in wb.sheetnames:
            print("✗ 未找到'汇总'工作表")
            return False, []
        
        ws = wb['汇总']
        update_log = []
        updated_count = 0
        
        # 遍历汇总表的每一行
        for row_idx in range(3, ws.max_row + 1):
            try:
                # 读取当前行的镇街、居委、环境类型
                town_cell = ws.cell(row=row_idx, column=1)
                community_cell = ws.cell(row=row_idx, column=2)
                env_type_cell = ws.cell(row=row_idx, column=3)
                
                town = str(town_cell.value).strip() if town_cell.value else ""
                community = str(community_cell.value).strip() if community_cell.value else ""
                env_type = str(env_type_cell.value).strip() if env_type_cell.value else ""
                
                # 跳过空行
                if not town or not env_type:
                    continue
                
                # 标准化名称进行匹配
                town_normalized = normalize_name(town)
                community_normalized = normalize_name(community)
                
                # 查找匹配的数据
                matched_key = None
                best_match_score = 0
                
                for key in summary_data:
                    key_town, key_community, key_env = key
                    
                    # 镇街匹配
                    town_match = (town_normalized in key_town or key_town in town_normalized or
                                town_normalized.replace('镇', '').replace('街', '') == 
                                key_town.replace('镇', '').replace('街', ''))
                    
                    # 居委匹配（如果汇总表中居委为空，则匹配任意居委）
                    community_match = (not community_normalized or 
                                     community_normalized in key_community or 
                                     key_community in community_normalized)
                    
                    # 环境类型精确匹配
                    env_match = (key_env == env_type)
                    
                    if town_match and community_match and env_match:
                        match_score = 1
                        if community_normalized and community_normalized == key_community:
                            match_score += 1  # 居委完全匹配加分
                        
                        if match_score > best_match_score:
                            best_match_score = match_score
                            matched_key = key
                
                if matched_key:
                    data = summary_data[matched_key]
                    
                    # 更新数据
                    ws.cell(row=row_idx, column=4, value=data['监测户数'])
                    ws.cell(row=row_idx, column=5, value=data['阳性数'])
                    ws.cell(row=row_idx, column=6, value=data['平均布雷图指数'])
                    ws.cell(row=row_idx, column=7, value=data['平均雌蚊密度'])
                    
                    updated_count += 1
                    
                    if updated_count <= 20:  # 记录前20个更新
                        update_log.append(f"✓ 第{row_idx}行: {town}-{community}-{env_type} -> "
                                        f"户数:{data['监测户数']}, 阳性:{data['阳性数']}")
                        
            except Exception as e:
                update_log.append(f"✗ 第{row_idx}行处理失败: {e}")
                continue
        
        # 保存文件
        wb.save(file_path)
        update_log.append(f"✓ 成功更新 {updated_count} 行数据")
        
        return True, update_log
        
    except Exception as e:
        return False, [f"✗ 更新失败: {e}"]

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🔄 开始更新a1.xlsx的汇总表")
    print("=" * 60)
    
    # 1. 备份原文件
    backup_path = backup_file(file_path)
    if not backup_path:
        response = input("⚠️  备份失败，是否继续？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return
    
    try:
        # 2. 提取数据
        print("\n📊 从明细表提取数据...")
        summary_data, processing_log = extract_summary_data(file_path)
        
        for log in processing_log:
            print(f"   {log}")
        
        if not summary_data:
            print("❌ 未能提取到任何数据")
            return
        
        print(f"\n✅ 成功提取 {len(summary_data)} 条汇总记录")
        
        # 3. 更新汇总表
        print("\n📝 更新汇总表...")
        success, update_log = update_summary_sheet(file_path, summary_data)
        
        for log in update_log[:10]:  # 只显示前10条日志
            print(f"   {log}")
        
        if len(update_log) > 10:
            print(f"   ... 还有 {len(update_log) - 10} 条更新记录")
        
        if success:
            print("\n🎉 汇总表更新完成！")
            
            # 4. 显示统计信息
            total_households = sum(data['监测户数'] for data in summary_data.values() if data['监测户数'])
            total_positive = sum(data['阳性数'] for data in summary_data.values() if data['阳性数'])
            
            print(f"\n📈 更新统计:")
            print(f"   • 处理记录数: {len(summary_data)}")
            print(f"   • 总监测户数: {total_households:,}")
            print(f"   • 总阳性数: {total_positive}")
            if total_households > 0:
                print(f"   • 总体阳性率: {(total_positive/total_households*100):.2f}%")
            
            print(f"\n💾 原文件已更新，备份文件: {backup_path}")
            
        else:
            print("\n❌ 汇总表更新失败")
            if backup_path:
                print(f"💾 可以从备份文件恢复: {backup_path}")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        if backup_path:
            print(f"💾 可以从备份文件恢复: {backup_path}")

if __name__ == "__main__":
    main()
