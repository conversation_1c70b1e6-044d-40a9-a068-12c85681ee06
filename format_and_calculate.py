#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式化和计算Excel汇总表
1. D-I列格式化为数字型（保留小数）
2. F列 = E列/D列 * 10（阳性率）
3. 根据广州市蚊媒密度分级表计算总成蚊密度分级
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import NamedStyle, Font, Alignment, PatternFill
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path.replace('.xlsx', '')}_格式化备份_{timestamp}.xlsx"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 备份文件失败: {e}")
        return None

def get_density_grade(bi_value, adi_value):
    """
    根据广州市蚊媒密度分级表确定密度等级
    参数:
    - bi_value: 布雷图指数(BI)
    - adi_value: 雌蚊密度(ADI)
    
    返回: (等级, 颜色, 风险级别, 防控级别)
    """
    # 处理空值或无效值
    if pd.isna(bi_value) or pd.isna(adi_value):
        return "0级", "蓝色", "无风险", "落实日常防蚊措施"
    
    bi = float(bi_value) if bi_value != 0 else 0
    adi = float(adi_value) if adi_value != 0 else 0
    
    # 根据分级表判断等级
    # 三级（红色）：BI>20 或 ADI>10
    if bi > 20 or adi > 10:
        return "三级", "红色", "高度风险", "启动紧急蚊媒控制措施"
    
    # 二级（橙色）：10<BI≤20 或 5<ADI≤10  
    elif (10 < bi <= 20) or (5 < adi <= 10):
        return "二级", "橙色", "中度风险", "启动加强蚊媒控制措施"
    
    # 一级（黄色）：5<BI≤10 或 2<ADI≤5
    elif (5 < bi <= 10) or (2 < adi <= 5):
        return "一级", "黄色", "低度风险", "启动蚊媒控制措施"
    
    # 0级（蓝色）：0≤BI≤5 或 0≤ADI≤2
    else:
        return "0级", "蓝色", "无风险", "落实日常防蚊措施"

def format_and_calculate_sheet(file_path):
    """格式化和计算汇总表"""
    try:
        # 加载工作簿
        wb = load_workbook(file_path)
        
        if '汇总' not in wb.sheetnames:
            print("✗ 未找到'汇总'工作表")
            return False
        
        ws = wb['汇总']
        
        print("开始格式化和计算...")
        
        # 创建数字格式样式
        number_style = NamedStyle(name="number_format")
        number_style.number_format = '0.00'  # 保留两位小数
        
        # 处理每一行数据
        processed_count = 0
        calculation_log = []

        for row_idx in range(4, ws.max_row + 1):  # 从第4行开始，跳过表头
            try:
                # 检查是否是数据行（有镇街和环境类型）
                town_cell = ws.cell(row=row_idx, column=1)
                env_cell = ws.cell(row=row_idx, column=3)

                # 跳过表头行、空行或合并单元格
                if (not town_cell.value or
                    str(town_cell.value).strip() in ['镇街', ''] or
                    not env_cell.value or
                    str(env_cell.value).strip() in ['环境类型', '']):
                    continue

                # 读取D、E、G、H列的值
                d_cell = ws.cell(row=row_idx, column=4)  # D列：监测户数
                e_cell = ws.cell(row=row_idx, column=5)  # E列：阳性数
                g_cell = ws.cell(row=row_idx, column=7)  # G列：BI指数
                h_cell = ws.cell(row=row_idx, column=8)  # H列：ADI密度

                # 安全获取数值，处理公式和字符串
                def safe_get_numeric_value(cell):
                    if cell.value is None:
                        return 0
                    if isinstance(cell.value, (int, float)):
                        return float(cell.value)
                    if isinstance(cell.value, str):
                        # 跳过公式
                        if cell.value.startswith('='):
                            return 0
                        try:
                            return float(cell.value)
                        except ValueError:
                            return 0
                    return 0

                d_val = safe_get_numeric_value(d_cell)
                e_val = safe_get_numeric_value(e_cell)
                g_val = safe_get_numeric_value(g_cell)
                h_val = safe_get_numeric_value(h_cell)
                
                # 计算F列：阳性率 = E列/D列 * 10
                if d_val > 0:
                    f_val = round((e_val / d_val) * 10, 4)
                else:
                    f_val = 0
                
                # 更新F列
                f_cell = ws.cell(row=row_idx, column=6)
                f_cell.value = f_val
                
                # 格式化D-I列为数字格式
                for col in range(4, 10):  # D到I列
                    cell = ws.cell(row=row_idx, column=col)

                    # 跳过合并单元格
                    if hasattr(cell, 'coordinate') and cell.coordinate in ws.merged_cells:
                        continue

                    try:
                        # 设置具体的数值
                        if col == 4:  # D列：监测户数
                            cell.value = d_val
                        elif col == 5:  # E列：阳性数
                            cell.value = e_val
                        elif col == 6:  # F列：阳性率
                            cell.value = f_val
                        elif col == 7:  # G列：BI指数
                            cell.value = g_val
                        elif col == 8:  # H列：ADI密度
                            cell.value = h_val
                        elif col == 9:  # I列：密度分级
                            cell.value = grade
                            continue  # 分级不需要数字格式

                        # 应用数字格式（除了I列）
                        if col != 9:
                            cell.number_format = '0.00'

                    except Exception as e:
                        # 如果是合并单元格或其他问题，跳过
                        continue
                
                # 计算蚊媒密度分级（基于G列BI和H列ADI）
                grade, color, risk, control = get_density_grade(g_val, h_val)
                
                # 将分级信息写入I列（如果需要的话）
                i_cell = ws.cell(row=row_idx, column=9)  # I列
                i_cell.value = grade
                
                processed_count += 1
                
                # 记录前10个计算示例
                if processed_count <= 10:
                    town = ws.cell(row=row_idx, column=1).value or ""
                    community = ws.cell(row=row_idx, column=2).value or ""
                    env_type = ws.cell(row=row_idx, column=3).value or ""
                    
                    calculation_log.append(
                        f"第{row_idx}行 {town}-{community}-{env_type}: "
                        f"户数:{d_val}, 阳性:{e_val}, 阳性率:{f_val}, "
                        f"BI:{g_val}, ADI:{h_val}, 等级:{grade}"
                    )
                
            except Exception as e:
                print(f"处理第{row_idx}行时出错: {e}")
                continue
        
        # 保存文件
        wb.save(file_path)
        
        print(f"✓ 成功处理 {processed_count} 行数据")
        
        # 显示计算示例
        print("\n计算示例:")
        for log in calculation_log:
            print(f"   {log}")
        
        return True
        
    except Exception as e:
        print(f"✗ 格式化和计算失败: {e}")
        return False

def analyze_density_distribution(file_path):
    """分析蚊媒密度分级分布"""
    try:
        wb = load_workbook(file_path)
        ws = wb['汇总']
        
        grade_stats = {"0级": 0, "一级": 0, "二级": 0, "三级": 0}
        risk_examples = {"一级": [], "二级": [], "三级": []}
        
        for row_idx in range(3, ws.max_row + 1):
            try:
                grade = ws.cell(row=row_idx, column=9).value  # I列
                if grade in grade_stats:
                    grade_stats[grade] += 1
                
                # 收集高风险区域示例
                if grade in ["一级", "二级", "三级"] and len(risk_examples[grade]) < 3:
                    town = ws.cell(row=row_idx, column=1).value or ""
                    community = ws.cell(row=row_idx, column=2).value or ""
                    env_type = ws.cell(row=row_idx, column=3).value or ""
                    bi_val = ws.cell(row=row_idx, column=7).value or 0
                    adi_val = ws.cell(row=row_idx, column=8).value or 0
                    
                    risk_examples[grade].append(
                        f"{town}-{community}-{env_type} (BI:{bi_val}, ADI:{adi_val})"
                    )
                    
            except Exception as e:
                continue
        
        print(f"\n📊 蚊媒密度分级统计:")
        total = sum(grade_stats.values())
        for grade, count in grade_stats.items():
            percentage = (count / total * 100) if total > 0 else 0
            color_map = {"0级": "🔵", "一级": "🟡", "二级": "🟠", "三级": "🔴"}
            print(f"   {color_map.get(grade, '⚪')} {grade}: {count} 个区域 ({percentage:.1f}%)")
        
        # 显示高风险区域示例
        for grade in ["三级", "二级", "一级"]:
            if risk_examples[grade]:
                risk_map = {"一级": "低度风险", "二级": "中度风险", "三级": "高度风险"}
                print(f"\n{grade}({risk_map[grade]})示例:")
                for example in risk_examples[grade]:
                    print(f"   • {example}")
        
        return grade_stats
        
    except Exception as e:
        print(f"分析密度分布时出错: {e}")
        return None

def main():
    """主函数"""
    file_path = "a1.xlsx"
    
    if not os.path.exists(file_path):
        print(f"✗ 文件 {file_path} 不存在")
        return
    
    print("🔧 开始格式化和计算Excel汇总表")
    print("=" * 60)
    
    # 1. 备份文件
    backup_path = backup_file(file_path)
    if not backup_path:
        response = input("⚠️  备份失败，是否继续？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return
    
    try:
        # 2. 格式化和计算
        print("\n📊 格式化D-I列并计算阳性率和密度分级...")
        success = format_and_calculate_sheet(file_path)
        
        if success:
            print("\n🎉 格式化和计算完成！")
            
            # 3. 分析密度分级分布
            grade_stats = analyze_density_distribution(file_path)
            
            print(f"\n💾 文件已更新，备份文件: {backup_path}")
            
            print(f"\n📋 处理内容:")
            print(f"   • D-I列格式化为数字型（保留2位小数）")
            print(f"   • F列计算阳性率 = E列/D列 × 10")
            print(f"   • I列根据BI和ADI值计算蚊媒密度分级")
            print(f"   • 分级标准：0级(蓝)、一级(黄)、二级(橙)、三级(红)")
            
        else:
            print("\n❌ 格式化和计算失败")
            if backup_path:
                print(f"💾 可以从备份文件恢复: {backup_path}")
    
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        if backup_path:
            print(f"💾 可以从备份文件恢复: {backup_path}")

if __name__ == "__main__":
    import os
    main()
